#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Sets up the PostgreSQL database for InnoBook encryption

.DESCRIPTION
    This script helps set up the required PostgreSQL extensions and
    configuration for the InnoBook encryption system.

.PARAMETER ConnectionString
    PostgreSQL connection string (optional, will read from appsettings.json if not provided)

.PARAMETER DatabaseName
    Database name (optional, will extract from connection string)

.PARAMETER Host
    PostgreSQL host (default: localhost)

.PARAMETER Port
    PostgreSQL port (default: 5432)

.PARAMETER Username
    PostgreSQL username (optional, will prompt if needed)

.PARAMETER Password
    PostgreSQL password (optional, will prompt if needed)

.EXAMPLE
    .\setup-database.ps1
    Setup using connection string from appsettings.json

.EXAMPLE
    .\setup-database.ps1 -Host localhost -DatabaseName innobook -Username postgres
    Setup with specific parameters
#>

param(
    [string]$ConnectionString,
    [string]$DatabaseName,
    [string]$Host = "localhost",
    [int]$Port = 5432,
    [string]$Username,
    [string]$Password
)

# Colors for output
$Red = "`e[31m"
$Green = "`e[32m"
$Yellow = "`e[33m"
$Blue = "`e[34m"
$Magenta = "`e[35m"
$Cyan = "`e[36m"
$Reset = "`e[0m"

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = $Reset
    )
    Write-Host "$Color$Message$Reset"
}

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-ColorOutput "=" * 60 $Cyan
    Write-ColorOutput "  $Title" $Cyan
    Write-ColorOutput "=" * 60 $Cyan
    Write-Host ""
}

function Write-Step {
    param([string]$Message)
    Write-ColorOutput "🔄 $Message" $Blue
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" $Green
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠️  $Message" $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" $Red
}

function Get-ConnectionStringFromConfig {
    try {
        if (Test-Path "appsettings.json") {
            $config = Get-Content "appsettings.json" | ConvertFrom-Json
            return $config.ConnectionStrings.InnoLogicielContext
        }
        elseif (Test-Path "appsettings.Development.json") {
            $config = Get-Content "appsettings.Development.json" | ConvertFrom-Json
            return $config.ConnectionStrings.InnoLogicielContext
        }
    }
    catch {
        Write-Warning "Could not read connection string from config files"
    }
    return $null
}

function Parse-ConnectionString {
    param([string]$ConnectionString)
    
    $params = @{}
    
    # Parse PostgreSQL connection string
    $parts = $ConnectionString -split ";"
    foreach ($part in $parts) {
        if ($part -match "^(.+?)=(.+)$") {
            $key = $matches[1].Trim()
            $value = $matches[2].Trim()
            
            switch ($key.ToLower()) {
                "host" { $params.Host = $value }
                "server" { $params.Host = $value }
                "port" { $params.Port = [int]$value }
                "database" { $params.Database = $value }
                "username" { $params.Username = $value }
                "user id" { $params.Username = $value }
                "password" { $params.Password = $value }
            }
        }
    }
    
    return $params
}

# Main script
try {
    Write-Header "InnoBook Database Encryption Setup"

    # Check if we're in the right directory
    if (-not (Test-Path "InnoBook.sln")) {
        Write-Error "Please run this script from the InnoBook project root directory"
        exit 1
    }

    # Get connection parameters
    if (-not $ConnectionString) {
        Write-Step "Reading connection string from configuration..."
        $ConnectionString = Get-ConnectionStringFromConfig
        
        if ($ConnectionString) {
            Write-Success "Found connection string in configuration"
            $connParams = Parse-ConnectionString $ConnectionString
            
            if (-not $DatabaseName) { $DatabaseName = $connParams.Database }
            if (-not $Username) { $Username = $connParams.Username }
            if (-not $Password) { $Password = $connParams.Password }
            if ($connParams.Host) { $Host = $connParams.Host }
            if ($connParams.Port) { $Port = $connParams.Port }
        }
        else {
            Write-Warning "Could not find connection string in configuration"
        }
    }

    # Prompt for missing parameters
    if (-not $DatabaseName) {
        $DatabaseName = Read-Host "Enter database name"
    }
    
    if (-not $Username) {
        $Username = Read-Host "Enter PostgreSQL username"
    }
    
    if (-not $Password) {
        $Password = Read-Host "Enter PostgreSQL password" -AsSecureString
        $Password = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($Password))
    }

    Write-ColorOutput "Database: $DatabaseName" $Magenta
    Write-ColorOutput "Host: $Host:$Port" $Magenta
    Write-ColorOutput "Username: $Username" $Magenta

    # Check if psql is available
    Write-Step "Checking PostgreSQL client..."
    try {
        $null = Get-Command psql -ErrorAction Stop
        Write-Success "PostgreSQL client (psql) found"
    }
    catch {
        Write-Error "PostgreSQL client (psql) not found. Please install PostgreSQL client tools."
        Write-Host "Download from: https://www.postgresql.org/download/"
        exit 1
    }

    # Test connection
    Write-Step "Testing database connection..."
    $env:PGPASSWORD = $Password
    $testResult = & psql -h $Host -p $Port -U $Username -d $DatabaseName -c "SELECT version();" 2>&1
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to connect to database"
        Write-Host $testResult
        exit 1
    }
    
    Write-Success "Database connection successful"

    # Run the setup script
    Write-Step "Setting up encryption extensions..."
    $setupScript = "Scripts\setup-database-encryption.sql"
    
    if (-not (Test-Path $setupScript)) {
        Write-Error "Setup script not found: $setupScript"
        exit 1
    }

    $setupResult = & psql -h $Host -p $Port -U $Username -d $DatabaseName -f $setupScript 2>&1
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to run setup script"
        Write-Host $setupResult
        exit 1
    }

    Write-Success "Database encryption setup completed!"
    
    # Show next steps
    Write-Host ""
    Write-ColorOutput "📋 Next Steps:" $Cyan
    Write-Host "1. Verify encryption key is set in appsettings.json"
    Write-Host "2. Test encryption: GET /api/Users/<USER>"
    Write-Host "3. Run migration preview: .\Scripts\migrate-encryption.bat --dry-run"
    Write-Host "4. Run actual migration: .\Scripts\migrate-encryption.bat"
    
    Write-Host ""
    Write-ColorOutput "🔒 Security Reminder:" $Yellow
    Write-Host "- Keep your encryption key secure and backed up"
    Write-Host "- Never commit encryption keys to source control"
    Write-Host "- Use environment variables in production"

}
catch {
    Write-Error "An error occurred: $($_.Exception.Message)"
    Write-Host $_.ScriptStackTrace
    exit 1
}
finally {
    # Clear password from environment
    $env:PGPASSWORD = $null
}

Write-Host ""
Write-ColorOutput "Database setup completed successfully!" $Green
