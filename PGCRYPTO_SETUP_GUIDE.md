# PostgreSQL pgcrypto Extension Setup Guide

## Problem

If you encounter this error when using the InnoBook encryption system:

```
Npgsql.PostgresException (0x80004005): 42883: function pgp_sym_encrypt(text, text) does not exist
```

This means the PostgreSQL `pgcrypto` extension is not enabled in your database.

## Solution

The `pgcrypto` extension provides cryptographic functions for PostgreSQL, including the `pgp_sym_encrypt` and `pgp_sym_decrypt` functions used by InnoBook's encryption system.

### Quick Fix (Automated Setup)

Run one of these setup scripts to automatically enable the extension:

#### Option 1: Windows Batch Script
```bash
Scripts\setup-database.bat
```

#### Option 2: PowerShell Script
```powershell
.\Scripts\setup-database.ps1
```

#### Option 3: Direct SQL
```bash
psql -h localhost -U username -d database_name -f Scripts\setup-database-encryption.sql
```

### Manual Setup

If you prefer to set up manually:

1. **Connect to your PostgreSQL database** as a superuser or database owner:
   ```bash
   psql -h localhost -U postgres -d your_database_name
   ```

2. **Enable the pgcrypto extension**:
   ```sql
   CREATE EXTENSION IF NOT EXISTS pgcrypto;
   ```

3. **Verify the extension is installed**:
   ```sql
   SELECT extname, extversion FROM pg_extension WHERE extname = 'pgcrypto';
   ```

4. **Test the encryption functions**:
   ```sql
   SELECT pgp_sym_decrypt(pgp_sym_encrypt('test', 'key'), 'key');
   ```
   This should return `test`.

## What the Setup Scripts Do

The automated setup scripts will:

1. ✅ Enable the `pgcrypto` extension
2. ✅ Test that encryption functions work
3. ✅ Display available crypto functions
4. ✅ Provide next steps and security recommendations

## Verification

After running the setup, you can verify everything works by:

1. **Test the encryption endpoint**:
   ```
   GET /api/Users/<USER>
   ```

2. **Run a migration preview**:
   ```bash
   Scripts\migrate-encryption.bat --dry-run
   ```

## Common Issues

### Permission Denied
If you get a permission error:
```
ERROR: permission denied to create extension "pgcrypto"
```

**Solution**: Connect as a PostgreSQL superuser (usually `postgres`):
```bash
psql -h localhost -U postgres -d your_database_name
```

### Extension Already Exists
If you see:
```
NOTICE: extension "pgcrypto" already exists, skipping
```

This is normal and means the extension is already installed.

### Connection Failed
If the setup script can't connect to the database:

1. **Check PostgreSQL is running**:
   ```bash
   # Windows
   net start postgresql-x64-14
   
   # Linux/Mac
   sudo systemctl start postgresql
   ```

2. **Verify connection details** in your `appsettings.json`:
   ```json
   {
     "ConnectionStrings": {
       "InnoLogicielContext": "Host=localhost;Port=5432;Database=innobook;Username=postgres;Password=yourpassword"
     }
   }
   ```

3. **Test connection manually**:
   ```bash
   psql -h localhost -p 5432 -U postgres -d innobook
   ```

## Security Notes

- **Backup First**: Always backup your database before making changes
- **Secure Keys**: Store encryption keys securely, never in source control
- **Environment Variables**: Use environment variables for production keys
- **Access Control**: Limit who can access the database and encryption keys

## Next Steps

After setting up pgcrypto:

1. **Configure Encryption Key**: Set `ENCRYPTION_KEY` in your `appsettings.json`
2. **Test Encryption**: Use the test endpoint to verify everything works
3. **Run Migration**: Migrate existing data to encrypted format
4. **Monitor**: Watch application logs for any encryption/decryption errors

## Files Created

The setup process creates these helpful files:

- `Scripts/setup-database-encryption.sql` - SQL script to enable pgcrypto
- `Scripts/setup-database.bat` - Windows batch script
- `Scripts/setup-database.ps1` - PowerShell script
- `Scripts/README.md` - Detailed migration documentation

## Support

If you continue to have issues:

1. Check the PostgreSQL logs for detailed error messages
2. Verify your PostgreSQL version supports pgcrypto (9.1+)
3. Ensure you have the necessary permissions
4. Contact your database administrator if needed

## Example: Complete Setup Process

```bash
# 1. Run the setup script
Scripts\setup-database.bat

# 2. Verify in your application
curl http://localhost:5000/api/Users/<USER>

# 3. Preview migration
Scripts\migrate-encryption.bat --dry-run

# 4. Run actual migration
Scripts\migrate-encryption.bat
```

That's it! Your InnoBook encryption system should now be working properly.
