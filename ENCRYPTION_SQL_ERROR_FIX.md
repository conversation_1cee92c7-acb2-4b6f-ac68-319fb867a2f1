# Fix for "column s.Value does not exist" Error in EncryptionService

## Problem

The InnoBook encryption system was throwing this error:

```
Encryption failed: 42703: column s.Value does not exist
POSITION: 8
```

This error occurred when trying to use the `EncryptAsync` and `DecryptAsync` methods in the `EncryptionService`.

## Root Cause

The issue was with how Entity Framework Core was handling the SQL queries for PostgreSQL's pgcrypto functions. The original code was using:

```csharp
// PROBLEMATIC CODE
var sql = "SELECT pgp_sym_encrypt(@p0, @p1)";
var encryptedBytes = await _context.Database
    .SqlQuery<byte[]>(FormattableStringFactory.Create(sql, plainText, _encryptionKey))
    .FirstOrDefaultAsync();
```

The problem was that `SqlQuery<T>` with `FormattableStringFactory.Create()` was not properly handling the parameter substitution for PostgreSQL, causing Entity Framework to misinterpret the query structure.

## Solution

Changed the SQL query execution to use `SqlQueryRaw<T>` with proper parameter placeholders:

### Before (Broken)
```csharp
var sql = "SELECT pgp_sym_encrypt(@p0, @p1)";
var encryptedBytes = await _context.Database
    .SqlQuery<byte[]>(FormattableStringFactory.Create(sql, plainText, _encryptionKey))
    .FirstOrDefaultAsync();
```

### After (Fixed)
```csharp
var sql = "SELECT pgp_sym_encrypt({0}, {1})";
var result = await _context.Database
    .SqlQueryRaw<byte[]>(sql, plainText, _encryptionKey)
    .FirstOrDefaultAsync();
```

## Changes Made

### 1. Updated EncryptAsync Method

**File**: `Services/Classes/EncryptionService.cs`

```csharp
public async Task<string?> EncryptAsync(string? plainText)
{
    if (string.IsNullOrEmpty(plainText))
        return plainText;

    try
    {
        // Use PostgreSQL pgcrypto to encrypt the data
        var sql = "SELECT pgp_sym_encrypt({0}, {1})";
        var result = await _context.Database
            .SqlQueryRaw<byte[]>(sql, plainText, _encryptionKey)
            .FirstOrDefaultAsync();

        return result != null ? Convert.ToBase64String(result) : null;
    }
    catch (Exception ex)
    {
        // Enhanced error handling...
    }
}
```

### 2. Updated DecryptAsync Method

```csharp
public async Task<string?> DecryptAsync(string? encryptedText)
{
    if (string.IsNullOrEmpty(encryptedText))
        return encryptedText;

    // Check if the string is valid Base64 before attempting to decrypt
    if (!EncryptionHelper.IsValidBase64String(encryptedText))
    {
        return encryptedText; // Return as-is if not Base64
    }

    try
    {
        // Convert Base64 back to bytes
        var encryptedBytes = Convert.FromBase64String(encryptedText);

        // Use PostgreSQL pgcrypto to decrypt the data
        var sql = "SELECT pgp_sym_decrypt({0}, {1})";
        var result = await _context.Database
            .SqlQueryRaw<string>(sql, encryptedBytes, _encryptionKey)
            .FirstOrDefaultAsync();

        return result;
    }
    catch (Exception ex)
    {
        // Enhanced error handling...
    }
}
```

### 3. Removed Unnecessary Using Directive

Removed `using System.Runtime.CompilerServices;` as it was no longer needed after removing `FormattableStringFactory.Create()`.

## Key Differences

| Aspect | Before (Broken) | After (Fixed) |
|--------|----------------|---------------|
| **Method** | `SqlQuery<T>()` | `SqlQueryRaw<T>()` |
| **Parameters** | `FormattableStringFactory.Create()` | Direct parameter array |
| **Placeholders** | `@p0, @p1` | `{0}, {1}` |
| **Parameter Handling** | Entity Framework interpolation | Raw SQL with parameters |

## Why This Fix Works

1. **SqlQueryRaw vs SqlQuery**: `SqlQueryRaw` is designed for raw SQL with parameters, while `SqlQuery` is for LINQ-style queries
2. **Parameter Placeholders**: `{0}, {1}` placeholders work correctly with `SqlQueryRaw` for PostgreSQL
3. **Direct Parameters**: Passing parameters directly as an array is more reliable than using `FormattableStringFactory`
4. **PostgreSQL Compatibility**: This approach is specifically compatible with Npgsql (PostgreSQL driver for .NET)

## Testing

You can test the fix using:

1. **Test Endpoint**: `GET /api/Users/<USER>
2. **SQL Test Script**: Run `test-encryption.sql` in your PostgreSQL database
3. **Migration Tool**: `Scripts\migrate-encryption.bat --dry-run`

## Verification

After applying the fix, the encryption service should:

- ✅ Successfully encrypt plain text to Base64 strings
- ✅ Successfully decrypt Base64 strings back to plain text
- ✅ Handle null/empty values gracefully
- ✅ Validate Base64 format before decryption
- ✅ Work with all entity encryption methods (User, Client, Company)

## Related Files Modified

- `Services/Classes/EncryptionService.cs` - Main fix applied here
- `test-encryption.sql` - Test script to verify PostgreSQL setup
- `ENCRYPTION_SQL_ERROR_FIX.md` - This documentation

## Prerequisites

Make sure you have:

1. ✅ PostgreSQL with pgcrypto extension enabled
2. ✅ Proper encryption key configured in appsettings.json
3. ✅ Database connection working
4. ✅ Latest code with the SQL query fixes

## Error Prevention

To prevent similar issues in the future:

1. **Use SqlQueryRaw** for raw SQL queries with PostgreSQL
2. **Test SQL queries** directly in PostgreSQL before implementing
3. **Use proper parameter placeholders** (`{0}, {1}` not `@p0, @p1`)
4. **Validate database setup** before running encryption operations

The encryption system should now work correctly without the "column s.Value does not exist" error.
