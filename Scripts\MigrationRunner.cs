using InnoBook.Scripts;
using InnoBook.Services.Classes;
using InnoBook.Services.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace InnoBook.Scripts
{
    /// <summary>
    /// Console application to run the data encryption migration
    /// Usage: dotnet run --project Scripts/MigrationRunner.cs
    /// </summary>
    public class MigrationRunner
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("=== InnoBook Data Encryption Migration ===");
            Console.WriteLine();

            // Parse command line arguments
            var dryRun = args.Contains("--dry-run");
            var verify = args.Contains("--verify");
            var force = args.Contains("--force");

            if (dryRun)
            {
                Console.WriteLine("🔍 DRY RUN MODE - No data will be modified");
            }

            if (!force && !dryRun)
            {
                Console.WriteLine("⚠️  WARNING: This will encrypt existing data in the database!");
                Console.WriteLine("   Make sure you have a backup before proceeding.");
                Console.WriteLine("   Use --dry-run to test without making changes.");
                Console.WriteLine("   Use --force to skip this confirmation.");
                Console.WriteLine();
                Console.Write("Do you want to continue? (y/N): ");
                
                var response = Console.ReadLine();
                if (response?.ToLower() != "y" && response?.ToLower() != "yes")
                {
                    Console.WriteLine("Migration cancelled.");
                    return;
                }
            }

            try
            {
                // Build configuration
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false)
                    .AddJsonFile("appsettings.Development.json", optional: true)
                    .AddEnvironmentVariables()
                    .Build();

                // Build service provider
                var services = new ServiceCollection();
                ConfigureServices(services, configuration);
                var serviceProvider = services.BuildServiceProvider();

                // Get required services
                var context = serviceProvider.GetRequiredService<InnoLogicielContext>();
                var encryptionService = serviceProvider.GetRequiredService<IEncryptionService>();
                var logger = serviceProvider.GetRequiredService<ILogger<DataEncryptionMigration>>();

                // Test database connection
                Console.WriteLine("🔗 Testing database connection...");
                await context.Database.CanConnectAsync();
                Console.WriteLine("✅ Database connection successful");

                // Test encryption service
                Console.WriteLine("🔐 Testing encryption service...");
                var testText = "Test encryption";
                var encrypted = await encryptionService.EncryptAsync(testText);
                var decrypted = await encryptionService.DecryptAsync(encrypted);
                
                if (testText == decrypted)
                {
                    Console.WriteLine("✅ Encryption service test successful");
                }
                else
                {
                    throw new Exception("Encryption service test failed");
                }

                // Create migration instance
                var migration = new DataEncryptionMigration(context, encryptionService, logger);

                if (verify)
                {
                    Console.WriteLine("🔍 Verifying existing migration...");
                    await migration.VerifyMigrationAsync();
                }
                else if (!dryRun)
                {
                    Console.WriteLine("🚀 Starting migration...");
                    await migration.RunMigrationAsync();
                    
                    Console.WriteLine();
                    Console.WriteLine("✅ Migration completed successfully!");
                    Console.WriteLine("🔍 Running verification...");
                    await migration.VerifyMigrationAsync();
                }
                else
                {
                    Console.WriteLine("🔍 DRY RUN - Would migrate the following:");
                    await ShowMigrationPreview(context);
                }

                Console.WriteLine();
                Console.WriteLine("=== Migration Complete ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                Environment.Exit(1);
            }
        }

        private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            // Add logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // Add database context
            services.AddDbContext<InnoLogicielContext>(options =>
            {
                var connectionString = configuration.GetConnectionString("InnoLogicielContext");
                options.UseNpgsql(connectionString).UseSnakeCaseNamingConvention();
            });

            // Add encryption service
            services.AddTransient<IEncryptionService, EncryptionService>();

            // Add configuration
            services.AddSingleton<IConfiguration>(configuration);
        }

        private static async Task ShowMigrationPreview(InnoLogicielContext context)
        {
            // Count users to migrate
            var usersCount = await context.Users
                .Where(u => !string.IsNullOrEmpty(u.FirstName) || !string.IsNullOrEmpty(u.LastName))
                .CountAsync();

            // Count clients to migrate
            var clientsCount = await context.Clients
                .Where(c => c.isActive && 
                           (!string.IsNullOrEmpty(c.FirstName) || 
                            !string.IsNullOrEmpty(c.LastName) || 
                            !string.IsNullOrEmpty(c.PhoneNumber) ||
                            !string.IsNullOrEmpty(c.AddressLine1) ||
                            !string.IsNullOrEmpty(c.AddressLine2)))
                .CountAsync();

            // Count companies to migrate
            var companiesCount = await context.Companies
                .Where(c => !string.IsNullOrEmpty(c.Phone) || 
                           !string.IsNullOrEmpty(c.Adress) || 
                           !string.IsNullOrEmpty(c.Adress2))
                .CountAsync();

            Console.WriteLine($"📊 Migration Preview:");
            Console.WriteLine($"   - Users to encrypt: {usersCount}");
            Console.WriteLine($"   - Clients to encrypt: {clientsCount}");
            Console.WriteLine($"   - Companies to encrypt: {companiesCount}");
            Console.WriteLine($"   - Total records: {usersCount + clientsCount + companiesCount}");
            Console.WriteLine();
            Console.WriteLine("Fields that will be encrypted:");
            Console.WriteLine("   - User: FirstName, LastName");
            Console.WriteLine("   - Client: FirstName, LastName, PhoneNumber, BusinessPhoneNumber, MobilePhoneNumber, AddressLine1, AddressLine2");
            Console.WriteLine("   - Company: Phone, Adress, Adress2");
        }
    }
}
