import { environment } from 'environments/environment';
import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

const UrlApi = environment.HOST_API + "/api";

@Injectable({
  providedIn: 'root'
})
export class StripeService {
  private http = inject(HttpClient);

  constructor() { }

  createPaymentSession(
    amount: number,
    currency: string = 'USD',
    description: string = 'Payment',
    paymentType: string = 'one_time',
    billingInterval: string = 'month',
    planId?: string,
    additionalUsers: number = 0
  ): Observable<any> {
    return this.http.post(UrlApi + '/Stripe/CreateSession', {
      amount,
      currency,
      description,
      paymentType,
      billingInterval,
      planId,
      additionalUsers
    });
  }

  verifyPayment(sessionId: string): Observable<any> {
    return this.http.get(UrlApi + `/Stripe/VerifyPayment?sessionId=${sessionId}`);
  }
}
