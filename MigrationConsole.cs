using InnoBook.Common;
using InnoBook.Entities;
using InnoBook.Services.Classes;
using InnoBook.Services.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace InnoBook
{
    /// <summary>
    /// Simple console application to migrate existing data to encrypted format
    /// Run with: dotnet run --project . -- MigrationConsole
    /// </summary>
    public class MigrationConsole
    {
        public static async Task RunMigration(string[] args)
        {
            Console.WriteLine("=== InnoBook Data Encryption Migration ===");
            Console.WriteLine();

            var dryRun = args.Contains("--dry-run");
            var verify = args.Contains("--verify");
            var force = args.Contains("--force");

            if (dryRun)
            {
                Console.WriteLine("🔍 DRY RUN MODE - No data will be modified");
            }

            if (!force && !dryRun && !verify)
            {
                Console.WriteLine("⚠️  WARNING: This will encrypt existing data in the database!");
                Console.WriteLine("   Make sure you have a backup before proceeding.");
                Console.WriteLine("   Add --dry-run to test without making changes.");
                Console.WriteLine("   Add --force to skip this confirmation.");
                Console.WriteLine();
                Console.Write("Do you want to continue? (y/N): ");
                
                var response = Console.ReadLine();
                if (response?.ToLower() != "y" && response?.ToLower() != "yes")
                {
                    Console.WriteLine("Migration cancelled.");
                    return;
                }
            }

            try
            {
                // Build configuration
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false)
                    .AddJsonFile("appsettings.Development.json", optional: true)
                    .AddEnvironmentVariables()
                    .Build();

                // Build service provider
                var services = new ServiceCollection();
                
                // Add logging
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });

                // Add database context
                services.AddDbContext<InnoLogicielContext>(options =>
                {
                    var connectionString = configuration.GetConnectionString("InnoLogicielContext");
                    options.UseNpgsql(connectionString).UseSnakeCaseNamingConvention();
                });

                // Add encryption service
                services.AddTransient<IEncryptionService, EncryptionService>();
                services.AddSingleton<IConfiguration>(configuration);

                var serviceProvider = services.BuildServiceProvider();

                // Get required services
                var context = serviceProvider.GetRequiredService<InnoLogicielContext>();
                var encryptionService = serviceProvider.GetRequiredService<IEncryptionService>();
                var logger = serviceProvider.GetRequiredService<ILogger<MigrationConsole>>();

                // Test database connection
                Console.WriteLine("🔗 Testing database connection...");
                await context.Database.CanConnectAsync();
                Console.WriteLine("✅ Database connection successful");

                // Test encryption service
                Console.WriteLine("🔐 Testing encryption service...");
                var testText = "Test encryption";
                var encrypted = await encryptionService.EncryptAsync(testText);
                var decrypted = await encryptionService.DecryptAsync(encrypted);
                
                if (testText == decrypted)
                {
                    Console.WriteLine("✅ Encryption service test successful");
                }
                else
                {
                    throw new Exception("Encryption service test failed");
                }

                if (verify)
                {
                    await VerifyMigration(context, encryptionService, logger);
                }
                else if (dryRun)
                {
                    await ShowMigrationPreview(context);
                }
                else
                {
                    await RunDataMigration(context, encryptionService, logger);
                    await VerifyMigration(context, encryptionService, logger);
                }

                Console.WriteLine();
                Console.WriteLine("✅ Migration process completed!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                Environment.Exit(1);
            }
        }

        private static async Task RunDataMigration(InnoLogicielContext context, IEncryptionService encryptionService, ILogger logger)
        {
            Console.WriteLine("🚀 Starting data migration...");

            // Migrate Users
            await MigrateUsers(context, encryptionService, logger);

            // Migrate Clients
            await MigrateClients(context, encryptionService, logger);

            // Migrate Companies
            await MigrateCompanies(context, encryptionService, logger);

            Console.WriteLine("✅ Data migration completed!");
        }

        private static async Task MigrateUsers(InnoLogicielContext context, IEncryptionService encryptionService, ILogger logger)
        {
            Console.WriteLine("👥 Migrating Users...");

            var users = await context.Users
                .Where(u => !string.IsNullOrEmpty(u.FirstName) || !string.IsNullOrEmpty(u.LastName))
                .ToListAsync();

            Console.WriteLine($"Found {users.Count} users to migrate");

            foreach (var user in users)
            {
                if (IsLikelyEncrypted(user.FirstName) && IsLikelyEncrypted(user.LastName))
                {
                    continue; // Skip already encrypted
                }

                user.FirstName = await encryptionService.EncryptAsync(user.FirstName);
                user.LastName = await encryptionService.EncryptAsync(user.LastName);
                user.UpdatedAt = DateTime.UtcNow;
            }

            await context.SaveChangesAsync();
            Console.WriteLine($"✅ Migrated {users.Count} users");
        }

        private static async Task MigrateClients(InnoLogicielContext context, IEncryptionService encryptionService, ILogger logger)
        {
            Console.WriteLine("👤 Migrating Clients...");

            var clients = await context.Clients
                .Where(c => c.isActive)
                .ToListAsync();

            Console.WriteLine($"Found {clients.Count} clients to migrate");

            foreach (var client in clients)
            {
                if (IsLikelyEncrypted(client.FirstName) && IsLikelyEncrypted(client.LastName))
                {
                    continue; // Skip already encrypted
                }

                client.FirstName = await encryptionService.EncryptAsync(client.FirstName);
                client.LastName = await encryptionService.EncryptAsync(client.LastName);
                client.PhoneNumber = await encryptionService.EncryptAsync(client.PhoneNumber);
                client.BusinessPhoneNumber = await encryptionService.EncryptAsync(client.BusinessPhoneNumber);
                client.MobilePhoneNumber = await encryptionService.EncryptAsync(client.MobilePhoneNumber);
                client.AddressLine1 = await encryptionService.EncryptAsync(client.AddressLine1);
                client.AddressLine2 = await encryptionService.EncryptAsync(client.AddressLine2);
                client.UpdatedAt = DateTime.UtcNow;
            }

            await context.SaveChangesAsync();
            Console.WriteLine($"✅ Migrated {clients.Count} clients");
        }

        private static async Task MigrateCompanies(InnoLogicielContext context, IEncryptionService encryptionService, ILogger logger)
        {
            Console.WriteLine("🏢 Migrating Companies...");

            var companies = await context.Companies.ToListAsync();

            Console.WriteLine($"Found {companies.Count} companies to migrate");

            foreach (var company in companies)
            {
                if (IsLikelyEncrypted(company.Phone) && IsLikelyEncrypted(company.Adress))
                {
                    continue; // Skip already encrypted
                }

                company.Phone = await encryptionService.EncryptAsync(company.Phone);
                company.Adress = await encryptionService.EncryptAsync(company.Adress);
                company.Adress2 = await encryptionService.EncryptAsync(company.Adress2);
                company.UpdatedAt = DateTime.UtcNow;
            }

            await context.SaveChangesAsync();
            Console.WriteLine($"✅ Migrated {companies.Count} companies");
        }

        private static async Task VerifyMigration(InnoLogicielContext context, IEncryptionService encryptionService, ILogger logger)
        {
            Console.WriteLine("🔍 Verifying migration...");

            // Test a few users
            var testUsers = await context.Users.Take(3).ToListAsync();
            foreach (var user in testUsers)
            {
                try
                {
                    var firstName = await encryptionService.DecryptAsync(user.FirstName);
                    var lastName = await encryptionService.DecryptAsync(user.LastName);
                    Console.WriteLine($"✅ User {user.Id}: '{firstName}' '{lastName}'");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Failed to decrypt user {user.Id}: {ex.Message}");
                }
            }

            Console.WriteLine("✅ Verification completed");
        }

        private static async Task ShowMigrationPreview(InnoLogicielContext context)
        {
            var usersCount = await context.Users
                .Where(u => !string.IsNullOrEmpty(u.FirstName) || !string.IsNullOrEmpty(u.LastName))
                .CountAsync();

            var clientsCount = await context.Clients
                .Where(c => c.isActive)
                .CountAsync();

            var companiesCount = await context.Companies.CountAsync();

            Console.WriteLine($"📊 Migration Preview:");
            Console.WriteLine($"   - Users to encrypt: {usersCount}");
            Console.WriteLine($"   - Clients to encrypt: {clientsCount}");
            Console.WriteLine($"   - Companies to encrypt: {companiesCount}");
            Console.WriteLine($"   - Total records: {usersCount + clientsCount + companiesCount}");
        }

        private static bool IsLikelyEncrypted(string? data)
        {
            return EncryptionHelper.IsLikelyEncryptedData(data);
        }
    }
}
