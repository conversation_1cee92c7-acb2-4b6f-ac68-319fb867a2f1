using InnoBook.Common;
using InnoBook.Entities;
using InnoBook.Services.Interface;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;

namespace InnoBook.Middlewares
{
    /// <summary>
    /// Entity Framework interceptor that automatically encrypts/decrypts sensitive data
    /// </summary>
    public class EncryptionInterceptor : SaveChangesInterceptor
    {
        private readonly IEncryptionService _encryptionService;

        public EncryptionInterceptor(IEncryptionService encryptionService)
        {
            _encryptionService = encryptionService;
        }

        public override async ValueTask<InterceptionResult<int>> SavingChangesAsync(
            DbContextEventData eventData,
            InterceptionResult<int> result,
            CancellationToken cancellationToken = default)
        {
            if (eventData.Context != null)
            {
                await EncryptEntitiesBeforeSaving(eventData.Context);
            }

            return await base.SavingChangesAsync(eventData, result, cancellationToken);
        }

        public override InterceptionResult<int> SavingChanges(
            DbContextEventData eventData,
            InterceptionResult<int> result)
        {
            if (eventData.Context != null)
            {
                // For synchronous operations, we need to handle this carefully
                // since our encryption service is async
                EncryptEntitiesBeforeSaving(eventData.Context).GetAwaiter().GetResult();
            }

            return base.SavingChanges(eventData, result);
        }

        private async Task EncryptEntitiesBeforeSaving(DbContext context)
        {
            var entries = context.ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified)
                .Where(e => IsEncryptableEntity(e.Entity));

            foreach (var entry in entries)
            {
                await EncryptEntity(entry.Entity);
            }
        }

        private bool IsEncryptableEntity(object entity)
        {
            return entity is User || entity is Client || entity is Company;
        }

        private async Task EncryptEntity(object entity)
        {
            switch (entity)
            {
                case User user:
                    await EncryptUserData(user);
                    break;
                case Client client:
                    await EncryptClientData(client);
                    break;
                case Company company:
                    await EncryptCompanyData(company);
                    break;
            }
        }

        private async Task EncryptUserData(User user)
        {
            // Only encrypt if the data appears to be plain text (not already encrypted)
            if (!string.IsNullOrEmpty(user.FirstName) && !EncryptionHelper.IsLikelyEncryptedData(user.FirstName))
            {
                user.FirstName = await _encryptionService.EncryptAsync(user.FirstName);
            }

            if (!string.IsNullOrEmpty(user.LastName) && !EncryptionHelper.IsLikelyEncryptedData(user.LastName))
            {
                user.LastName = await _encryptionService.EncryptAsync(user.LastName);
            }
        }

        private async Task EncryptClientData(Client client)
        {
            // Only encrypt if the data appears to be plain text
            if (!string.IsNullOrEmpty(client.FirstName) && !EncryptionHelper.IsLikelyEncryptedData(client.FirstName))
            {
                client.FirstName = await _encryptionService.EncryptAsync(client.FirstName);
            }

            if (!string.IsNullOrEmpty(client.LastName) && !EncryptionHelper.IsLikelyEncryptedData(client.LastName))
            {
                client.LastName = await _encryptionService.EncryptAsync(client.LastName);
            }

            if (!string.IsNullOrEmpty(client.PhoneNumber) && !EncryptionHelper.IsLikelyEncryptedData(client.PhoneNumber))
            {
                client.PhoneNumber = await _encryptionService.EncryptAsync(client.PhoneNumber);
            }

            if (!string.IsNullOrEmpty(client.BusinessPhoneNumber) && !EncryptionHelper.IsLikelyEncryptedData(client.BusinessPhoneNumber))
            {
                client.BusinessPhoneNumber = await _encryptionService.EncryptAsync(client.BusinessPhoneNumber);
            }

            if (!string.IsNullOrEmpty(client.MobilePhoneNumber) && !EncryptionHelper.IsLikelyEncryptedData(client.MobilePhoneNumber))
            {
                client.MobilePhoneNumber = await _encryptionService.EncryptAsync(client.MobilePhoneNumber);
            }

            if (!string.IsNullOrEmpty(client.AddressLine1) && !EncryptionHelper.IsLikelyEncryptedData(client.AddressLine1))
            {
                client.AddressLine1 = await _encryptionService.EncryptAsync(client.AddressLine1);
            }

            if (!string.IsNullOrEmpty(client.AddressLine2) && !EncryptionHelper.IsLikelyEncryptedData(client.AddressLine2))
            {
                client.AddressLine2 = await _encryptionService.EncryptAsync(client.AddressLine2);
            }
        }

        private async Task EncryptCompanyData(Company company)
        {
            // Only encrypt if the data appears to be plain text
            if (!string.IsNullOrEmpty(company.Phone) && !EncryptionHelper.IsLikelyEncryptedData(company.Phone))
            {
                company.Phone = await _encryptionService.EncryptAsync(company.Phone);
            }

            if (!string.IsNullOrEmpty(company.Adress) && !EncryptionHelper.IsLikelyEncryptedData(company.Adress))
            {
                company.Adress = await _encryptionService.EncryptAsync(company.Adress);
            }

            if (!string.IsNullOrEmpty(company.Adress2) && !EncryptionHelper.IsLikelyEncryptedData(company.Adress2))
            {
                company.Adress2 = await _encryptionService.EncryptAsync(company.Adress2);
            }
        }
    }
}
