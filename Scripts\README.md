# InnoBook Data Encryption Migration

This directory contains scripts to migrate existing unencrypted data to encrypted format after implementing the encryption system.

## Overview

The encryption system encrypts the following sensitive fields:

### User Entity
- `FirstName`
- `LastName`

### Client Entity
- `FirstName`
- `LastName`
- `PhoneNumber`
- `BusinessPhoneNumber`
- `MobilePhoneNumber`
- `AddressLine1`
- `AddressLine2`

### Company Entity
- `Phone`
- `Adress`
- `Adress2`

## Migration Scripts

### 1. Windows Batch Script (Recommended for Windows)

```bash
# Preview what would be migrated (dry run)
Scripts\migrate-encryption.bat --dry-run

# Run the actual migration
Scripts\migrate-encryption.bat

# Skip confirmation prompt
Scripts\migrate-encryption.bat --force

# Verify existing migration
Scripts\migrate-encryption.bat --verify
```

### 2. PowerShell Script

```powershell
# Preview what would be migrated
.\Scripts\run-encryption-migration.ps1 -DryRun

# Run the migration
.\Scripts\run-encryption-migration.ps1

# Skip confirmation
.\Scripts\run-encryption-migration.ps1 -Force

# Verify migration
.\Scripts\run-encryption-migration.ps1 -Verify

# Specify environment
.\Scripts\run-encryption-migration.ps1 -Environment Production
```

### 3. Direct .NET Command

```bash
# Preview migration
dotnet run -- migrate-encryption --dry-run

# Run migration
dotnet run -- migrate-encryption

# Verify migration
dotnet run -- migrate-encryption --verify

# Force migration (skip confirmation)
dotnet run -- migrate-encryption --force
```

## Before Running Migration

### 1. Database Setup (REQUIRED)
**You must set up the database encryption extensions first!**

Run the database setup script to enable pgcrypto extension:

```bash
# Windows (Batch)
Scripts\setup-database.bat

# Windows (PowerShell)
.\Scripts\setup-database.ps1

# Manual SQL (if you prefer)
psql -h localhost -U username -d database_name -f Scripts\setup-database-encryption.sql
```

This will:
- Enable the `pgcrypto` extension in PostgreSQL
- Test that encryption functions work
- Verify the database is ready for encryption

### 2. Prerequisites
- ✅ pgcrypto extension enabled (use setup script above)
- Verify the `ENCRYPTION_KEY` is properly configured in appsettings
- Make sure the application builds successfully

### 2. Backup Your Database
**CRITICAL**: Always backup your database before running the migration!

```sql
-- PostgreSQL backup command
pg_dump -h localhost -U username -d database_name > backup_before_encryption.sql
```

### 3. Test the Encryption Service
Run a dry-run first to ensure everything is working:

```bash
Scripts\migrate-encryption.bat --dry-run
```

## Migration Process

The migration script will:

1. **Test Database Connection**: Verify it can connect to the database
2. **Test Encryption Service**: Verify encryption/decryption works
3. **Process in Batches**: Migrate data in batches of 50 records to avoid memory issues
4. **Skip Already Encrypted Data**: Automatically detect and skip data that appears to be already encrypted
5. **Update Timestamps**: Set `UpdatedAt` field for migrated records
6. **Verify Migration**: Test decryption on sample records

## Safety Features

- **Dry Run Mode**: Preview what would be migrated without making changes
- **Encryption Detection**: Automatically skips data that appears to already be encrypted
- **Batch Processing**: Processes data in small batches to avoid memory issues
- **Error Handling**: Stops on errors to prevent data corruption
- **Verification**: Tests decryption after migration

## Troubleshooting

### Common Issues

1. **"function pgp_sym_encrypt(text, text) does not exist" Error**
   - **Solution**: Run the database setup script first!
   - `Scripts\setup-database.bat` (Windows)
   - `.\Scripts\setup-database.ps1` (PowerShell)
   - Or manually: `CREATE EXTENSION IF NOT EXISTS pgcrypto;`

2. **"Failed to encrypt data" Error**
   - Check that pgcrypto extension is enabled (see issue #1 above)
   - Verify the encryption key is properly configured
   - Check database connection string

2. **"Build failed" Error**
   - Run `dotnet restore` first
   - Check for compilation errors
   - Ensure all dependencies are installed

3. **"Database connection failed" Error**
   - Verify connection string in appsettings.json
   - Check database server is running
   - Verify credentials and permissions

### Manual Verification

After migration, you can manually verify the encryption:

```sql
-- Check if data looks encrypted (should be Base64 strings)
SELECT id, first_name, last_name FROM users LIMIT 5;

-- Data should look like: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### Rollback

If you need to rollback:

1. Restore from your backup:
   ```bash
   psql -h localhost -U username -d database_name < backup_before_encryption.sql
   ```

2. Or manually decrypt data using the test endpoint:
   ```
   GET /api/Users/<USER>
   ```

## Environment Configuration

Make sure your appsettings.json has the encryption key:

```json
{
  "ENCRYPTION_KEY": "your-32-character-encryption-key-here",
  "ConnectionStrings": {
    "InnoLogicielContext": "your-connection-string"
  }
}
```

## Post-Migration Steps

1. **Test Application**: Ensure the application works with encrypted data
2. **Monitor Logs**: Watch for any decryption errors
3. **Run Verification**: Use `--verify` flag to test decryption
4. **Update Documentation**: Document that encryption is now enabled
5. **Secure Backup**: Ensure new backups are properly secured since they contain encrypted data

## Support

If you encounter issues:

1. Check the application logs for detailed error messages
2. Run with `--dry-run` to test without making changes
3. Verify your database backup is complete and restorable
4. Contact the development team with specific error messages
