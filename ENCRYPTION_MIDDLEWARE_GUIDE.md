# Adding Encryption Service to Database Middleware

This guide explains how to integrate the encryption service with Entity Framework to automatically encrypt/decrypt data when saving to and loading from the database.

## Approach 1: Entity Framework Interceptors (Current Implementation)

### Files Created

1. **`Middlewares/EncryptionInterceptor.cs`** - Encrypts data before saving
2. **`Middlewares/DecryptionInterceptor.cs`** - Decrypts data after loading
3. **Updated `Program.cs`** - Registers interceptors with DbContext

### How It Works

The interceptors automatically:
- **Encrypt** sensitive data before saving to database
- **Decrypt** sensitive data after loading from database
- **Detect** if data is already encrypted to avoid double-encryption
- **Handle** User, Client, and Company entities

### Configuration in Program.cs

```csharp
// Register encryption interceptors
builder.Services.AddScoped<EncryptionInterceptor>();
builder.Services.AddScoped<EntityMaterializationInterceptor>();

// Configure DbContext with interceptors
builder.Services.AddDbContext<InnoLogicielContext>((serviceProvider, options) =>
{
    var connectionString = builder.Configuration.GetConnectionString("InnoLogicielContext");
    
    // Get the encryption interceptors from DI
    var encryptionInterceptor = serviceProvider.GetService<EncryptionInterceptor>();
    var materializationInterceptor = serviceProvider.GetService<EntityMaterializationInterceptor>();
    
    options.UseNpgsql(connectionString)
           .UseSnakeCaseNamingConvention();
    
    // Add interceptors if they are available
    if (encryptionInterceptor != null)
    {
        options.AddInterceptors(encryptionInterceptor);
    }
    
    if (materializationInterceptor != null)
    {
        options.AddInterceptors(materializationInterceptor);
    }
});
```

### Encrypted Fields

**User Entity:**
- `FirstName`
- `LastName`

**Client Entity:**
- `FirstName`
- `LastName`
- `PhoneNumber`
- `BusinessPhoneNumber`
- `MobilePhoneNumber`
- `AddressLine1`
- `AddressLine2`

**Company Entity:**
- `Phone`
- `Adress` (Address)
- `Adress2` (Address Line 2)

## Approach 2: Extended DbContext (Alternative)

### File Created

**`Contexts/EncryptedInnoLogicielContext.cs`** - Extended DbContext with built-in encryption

### How to Use Alternative Approach

If you prefer the DbContext override approach instead of interceptors:

1. **Replace the DbContext registration** in `Program.cs`:

```csharp
// Replace this:
builder.Services.AddDbContext<InnoLogicielContext>(...);

// With this:
builder.Services.AddDbContext<InnoLogicielContext, EncryptedInnoLogicielContext>((serviceProvider, options) =>
{
    var connectionString = builder.Configuration.GetConnectionString("InnoLogicielContext");
    options.UseNpgsql(connectionString).UseSnakeCaseNamingConvention();
});
```

2. **Update service registrations** to use the encrypted context:

```csharp
// Services will automatically get the EncryptedInnoLogicielContext
```

## How Encryption Detection Works

The system uses `EncryptionHelper.IsLikelyEncryptedData()` to determine if data is already encrypted:

```csharp
public static bool IsLikelyEncryptedData(string? data, int minLength = 20)
{
    if (string.IsNullOrEmpty(data))
        return false;

    // Check if it's a valid Base64 string that's likely to be encrypted data
    return IsValidBase64String(data) && data.Length > minLength;
}
```

This prevents:
- ✅ Double-encryption of already encrypted data
- ✅ Attempting to decrypt plain text data
- ✅ Performance issues from unnecessary operations

## Benefits

### Automatic Operation
- **No code changes** required in services or controllers
- **Transparent** to application logic
- **Consistent** encryption across all database operations

### Safety Features
- **Detects encrypted data** to prevent double-encryption
- **Handles errors gracefully** without breaking the application
- **Validates Base64** format before decryption attempts

### Performance
- **Only processes** entities that need encryption/decryption
- **Skips operations** for already processed data
- **Minimal overhead** for non-encrypted entities

## Testing the Middleware

### 1. Test Automatic Encryption

```csharp
// Create a new user - data should be automatically encrypted
var user = new User 
{ 
    FirstName = "John", 
    LastName = "Doe",
    Email = "<EMAIL>"
};

context.Users.Add(user);
await context.SaveChangesAsync(); // FirstName and LastName are encrypted here

// Check database - should see Base64 encrypted data
```

### 2. Test Automatic Decryption

```csharp
// Load user from database - data should be automatically decrypted
var user = await context.Users.FirstAsync(u => u.Email == "<EMAIL>");

// user.FirstName should be "John" (decrypted)
// user.LastName should be "Doe" (decrypted)
```

### 3. Test Mixed Data

```csharp
// The system handles mixed encrypted/plain text data gracefully
var users = await context.Users.ToListAsync();
// Some users might have encrypted data, others plain text
// All will be properly handled
```

## Configuration Options

### Enable/Disable Encryption

You can conditionally enable encryption based on configuration:

```csharp
// In Program.cs
var encryptionEnabled = builder.Configuration.GetValue<bool>("Encryption:Enabled", true);

if (encryptionEnabled)
{
    builder.Services.AddScoped<EncryptionInterceptor>();
    builder.Services.AddScoped<EntityMaterializationInterceptor>();
}
```

### Customize Encrypted Fields

To add more fields or entities, modify the interceptor methods:

```csharp
private async Task EncryptUserData(User user)
{
    // Add more fields as needed
    if (!string.IsNullOrEmpty(user.Email) && !EncryptionHelper.IsLikelyEncryptedData(user.Email))
    {
        user.Email = await _encryptionService.EncryptAsync(user.Email);
    }
}
```

## Migration Strategy

### For Existing Data

1. **Run the migration tool** first to encrypt existing data:
   ```bash
   Scripts\migrate-encryption.bat
   ```

2. **Enable middleware** after migration is complete

3. **Test thoroughly** with a backup database first

### For New Applications

1. **Enable middleware** from the start
2. **All new data** will be automatically encrypted
3. **No migration** needed

## Troubleshooting

### Common Issues

**Issue**: Double encryption occurring
**Solution**: Check `IsLikelyEncryptedData()` logic and ensure it correctly identifies encrypted data

**Issue**: Decryption errors
**Solution**: Verify encryption key is consistent and pgcrypto extension is enabled

**Issue**: Performance impact
**Solution**: Monitor database operations and consider caching for frequently accessed data

### Debugging

Enable detailed logging to see encryption operations:

```csharp
// Add to EncryptionInterceptor
Console.WriteLine($"Encrypting {entity.GetType().Name}: {fieldName}");
```

## Security Considerations

1. **Encryption Key**: Store securely, never in source control
2. **Key Rotation**: Plan for periodic key rotation
3. **Backup Strategy**: Ensure backups include encryption keys
4. **Access Control**: Limit who can access encryption configuration
5. **Audit Trail**: Log encryption/decryption operations for compliance

## Performance Tips

1. **Batch Operations**: Middleware handles batch saves efficiently
2. **Selective Encryption**: Only encrypt truly sensitive fields
3. **Caching**: Consider caching decrypted data for read-heavy scenarios
4. **Indexing**: Encrypted fields cannot be efficiently indexed for searching

The encryption middleware provides a robust, automatic solution for protecting sensitive data in your InnoBook application.
