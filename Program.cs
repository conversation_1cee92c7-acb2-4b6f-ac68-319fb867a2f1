using InnoBook.Enum;
using InnoBook.Filter;
using InnoBook.Interface;
using InnoBook.Middlewares;
using InnoBook.Services.Classes;
using InnoBook.Services.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.SpaServices.AngularCli;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.FileProviders;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Text;
using UData.RPA.Web.Mobile.Middleware;
var MyAllowSpecificOrigins = "_myAllowSpecificOrigins";
var builder = WebApplication.CreateBuilder(args);
string prodCors = "_prod";

builder.Services.AddHttpContextAccessor();
var env = builder.Environment;
builder.Services.AddCors(options =>
{
    options.AddPolicy(name: prodCors,
                      builder =>
                      {
                          if (env.IsDevelopment())
                          {
                              builder.AllowAnyHeader()
                                  .AllowAnyMethod()
                                  .AllowAnyOrigin();
                          }
                          else
                          {
                              builder.AllowAnyHeader()
                                  .WithMethods("POST", "GET", "PUT", "DELETE")
                                  .WithOrigins("https://app.innobooks.net")
                                  .WithOrigins("https://test.innobooks.net");

                          }
                      });
});
builder.Services.AddTransient<ICompanyService, CompanyService>();
builder.Services.AddTransient<IBusinessService, BusinessService>();
builder.Services.AddTransient<IUserIdentity, UserIdentityService>();
builder.Services.AddTransient<IClientService, ClientService>();
builder.Services.AddTransient<IProjectService, ProjectService>();
builder.Services.AddTransient<IMemberService, MemberService>();
builder.Services.AddTransient<ITimeTrackingService, TimeTrackingServices>();
builder.Services.AddTransient<IService, Sevices>();
builder.Services.AddTransient<IMailService, MailService>();
builder.Services.AddTransient<ITaxService, TaxService>();
builder.Services.AddTransient<IInvoiceService, InvoiceService>();
builder.Services.AddTransient<IPaymentService, PaymentService>();
builder.Services.AddTransient<ICategoryService, CategoryService>();
builder.Services.AddTransient<IExpensesService, ExpensesService>();
builder.Services.AddTransient<IMerchantServices, MerchantServices>();
builder.Services.AddTransient<IPasswordHasher, PasswordHasher>();
builder.Services.AddTransient<IItemService, ItemService>();
builder.Services.AddTransient<IRoleServices, RoleServices>();
builder.Services.AddTransient<ICompanyTaxService, CompanyTaxService>();
builder.Services.AddTransient<IStripeService, StripeService>();
builder.Services.AddTransient<IPlanService, PlanService>();
builder.Services.AddTransient<IEncryptionService, EncryptionService>();

//// Register encryption interceptors
builder.Services.AddScoped<EncryptionInterceptor>();
//builder.Services.AddScoped<EntityMaterializationInterceptor>();
builder.Services.AddScoped<SqlEncryptionInterceptor>();

builder.Services.AddDbContext<InnoLogicielContext>((serviceProvider, options) =>
{
    var connectionString = builder.Configuration.GetConnectionString("InnoLogicielContext");
    options.UseNpgsql(connectionString)
       .UseSnakeCaseNamingConvention();

    // Get the encryption interceptors from DI
    var encryptionInterceptor = serviceProvider.GetService<EncryptionInterceptor>();
    // For entity-level encryption (in-memory), use these interceptors instead
    if (encryptionInterceptor != null)
    {
        options.AddInterceptors(encryptionInterceptor);
    } else
    {
    }

    //var materializationInterceptor = serviceProvider.GetService<EntityMaterializationInterceptor>();
    //var sqlEncryptionInterceptor = serviceProvider.GetService<SqlEncryptionInterceptor>();

    // Add interceptors if they are available
    // Note: You can choose between entity-level interceptors OR SQL-level interceptor
    // For SQL-level encryption (modifying queries), use SqlEncryptionInterceptor
    //if (sqlEncryptionInterceptor != null)
    //{
    //    options.AddInterceptors(sqlEncryptionInterceptor);
    //}



    //if (materializationInterceptor != null)
    //{
    //    options.AddInterceptors(materializationInterceptor);
    //}
});

builder.Services.AddHttpClient();
builder.Services.AddAuthentication(x =>
{
    x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
}).AddCookie(UWIPConstants.TwoFactorUserIdScheme, options =>
{
    options.Cookie.Name = UWIPConstants.TwoFactorUserIdScheme;
    options.ExpireTimeSpan = TimeSpan.FromMinutes(5);
    options.Cookie.IsEssential = true;
    options.Cookie.HttpOnly = true;
    options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
    options.Cookie.SameSite = SameSiteMode.None;
}).AddCookie(UWIPConstants.TwoFactorRememberMeScheme, options =>
{
    options.Cookie.Name = UWIPConstants.TwoFactorRememberMeScheme;
    options.ExpireTimeSpan = TimeSpan.FromMinutes(5);
    options.Cookie.IsEssential = true;
    options.Cookie.HttpOnly = true;
    options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
    options.Cookie.SameSite = SameSiteMode.None;
}).AddCookie(UWIPConstants.ApplicationScheme, options =>
{
    options.Cookie.Name = UWIPConstants.ApplicationScheme;
    options.ExpireTimeSpan = TimeSpan.FromMinutes(5);
    options.Cookie.IsEssential = true;
    options.Cookie.HttpOnly = true;
    options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
    options.Cookie.SameSite = SameSiteMode.None;
})
   .AddJwtBearer(x =>
   {
       x.RequireHttpsMetadata = true;
       x.SaveToken = true;
       x.TokenValidationParameters = new TokenValidationParameters
       {
           ValidateIssuerSigningKey = false,
           IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(builder.Configuration.GetSection("Jwt:SecretKey").Value)),
           ValidateIssuer = true,
           ValidateAudience = true,
           ValidateLifetime = true,
           ClockSkew = TimeSpan.Zero,
           NameClaimType = System.Security.Claims.ClaimTypes.Name,
           RoleClaimType = System.Security.Claims.ClaimTypes.Role,
           ValidAudience = builder.Configuration["Jwt:Audience"],
           ValidIssuer = builder.Configuration["Jwt:Issuer"],
       };
       x.Events = new JwtBearerEvents
       {
           OnMessageReceived = context =>
           {
               if (context.Request.Headers.TryGetValue("g", out Microsoft.Extensions.Primitives.StringValues value))
               {
                   context.Token = value;
               }
               return Task.CompletedTask;
           },
           OnTokenValidated = context =>
           {
               var userClaims = context.Principal.Claims;
               return Task.CompletedTask;
           },
           OnAuthenticationFailed = context =>
           {
               //Log.Information("Authentication failed: {ErrorMessage}", context.Exception.Message);
               return Task.CompletedTask;
           }
       };
   });
builder.Services.AddAuthorization();
//builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(
    c =>
    {
        var securityScheme = new OpenApiSecurityScheme
        {
            Name = "Authorization",
            Description = "Please enter into field the word 'Bearer' following by space and JWT",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            //Scheme = "bearer", // must be lower case
            Reference = new OpenApiReference
            {
                Id = "Bearer",
                Type = ReferenceType.SecurityScheme
            }
        };
        c.AddSecurityDefinition(securityScheme.Reference.Id, securityScheme);
        c.AddSecurityRequirement(new OpenApiSecurityRequirement
                            {
                                            {securityScheme, new string[] { }}
                            });
    });

builder.Services.AddSpaStaticFiles(configuration =>
{
    configuration.RootPath = "ClientApp/dist";
});

builder.Services.AddControllers().AddNewtonsoftJson(options =>
options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore);

builder.Services.AddControllers(options =>
    options.Filters.Add<EmailExceptionFilter>());

var app = builder.Build();

using (var scope = app.Services.CreateScope())
{
    var dbContext = scope.ServiceProvider.GetRequiredService<InnoLogicielContext>();
    dbContext.Database.Migrate();
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

//builder.Services.AddScoped(typeof(ISocialService<GoogleAuthService>), typeof(GoogleAuthService));

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseCors(MyAllowSpecificOrigins);
app.UseStaticFiles();
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(
                  Path.Combine(Directory.GetCurrentDirectory(), "Uploads")),
    RequestPath = "/Uploads"
});
app.UseRouting();

if (env.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseCors(prodCors);
}
else
{
    app.UseCors(prodCors);
    app.UseHsts();
}

app.UseMiddleware<CSPMiddleware>();
app.UseSpaStaticFiles();
app.UseAuthentication();
app.UseAuthorization();

app.UseEndpoints(endpoints =>
{
    endpoints.MapControllers();
});

app.UseSpa(spa =>
    {
        // To learn more about options for serving an Angular SPA from ASP.NET Core,
        // see https://go.microsoft.com/fwlink/?linkid=864501

        spa.Options.SourcePath = "ClientApp";
        spa.Options.DefaultPage = "/index.html";
        spa.Options.StartupTimeout = new TimeSpan(0, 5, 0);
        if (app.Environment.IsDevelopment())
        {
            spa.UseAngularCliServer(npmScript: "start");
        }
    });

Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("Mgo+DSMBMAY9C3t2XFhhQlJHfV5AQmBIYVp/TGpJfl96cVxMZVVBJAtUQF1hTH5Wd0NiX3xbcHJXT2hVWkZ/\r\n");


app.Run();
