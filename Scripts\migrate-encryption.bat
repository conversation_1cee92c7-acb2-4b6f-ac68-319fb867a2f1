@echo off
setlocal enabledelayedexpansion

echo ===============================================
echo   InnoBook Data Encryption Migration
echo ===============================================
echo.

REM Check if we're in the right directory
if not exist "InnoBook.sln" (
    echo ERROR: Please run this script from the InnoBook project root directory
    pause
    exit /b 1
)

REM Parse command line arguments
set DRY_RUN=false
set VERIFY=false
set FORCE=false

:parse_args
if "%~1"=="" goto :done_parsing
if /i "%~1"=="--dry-run" set DRY_RUN=true
if /i "%~1"=="--verify" set VERIFY=true
if /i "%~1"=="--force" set FORCE=true
shift
goto :parse_args
:done_parsing

REM Show mode
if "%DRY_RUN%"=="true" (
    echo Mode: DRY RUN - No changes will be made
    echo.
)
if "%VERIFY%"=="true" (
    echo Mode: VERIFY - Testing existing encryption
    echo.
)

REM Warning for actual migration
if "%DRY_RUN%"=="false" if "%VERIFY%"=="false" if "%FORCE%"=="false" (
    echo WARNING: This will encrypt existing data in the database!
    echo Make sure you have a backup before proceeding.
    echo.
    echo Use --dry-run to test without making changes
    echo Use --verify to test existing encryption
    echo Use --force to skip this confirmation
    echo.
    set /p "confirm=Do you want to continue? (y/N): "
    if /i not "!confirm!"=="y" if /i not "!confirm!"=="yes" (
        echo Migration cancelled.
        pause
        exit /b 0
    )
)

echo Building project...
dotnet build --configuration Release --verbosity minimal
if errorlevel 1 (
    echo ERROR: Build failed
    pause
    exit /b 1
)
echo Build completed successfully.
echo.

echo Starting migration...
echo.

REM Build arguments
set ARGS=
if "%DRY_RUN%"=="true" set ARGS=%ARGS% --dry-run
if "%VERIFY%"=="true" set ARGS=%ARGS% --verify
if "%FORCE%"=="true" set ARGS=%ARGS% --force

REM Run the migration
dotnet run --project . EncryptionMigrationTool %ARGS%

if errorlevel 1 (
    echo.
    echo ERROR: Migration failed
    pause
    exit /b 1
)

echo.
echo Migration completed successfully!

if "%DRY_RUN%"=="false" if "%VERIFY%"=="false" (
    echo.
    echo Next steps:
    echo 1. Test your application with encrypted data
    echo 2. Monitor application logs for any issues
    echo 3. Run verification: migrate-encryption.bat --verify
)

echo.
pause
