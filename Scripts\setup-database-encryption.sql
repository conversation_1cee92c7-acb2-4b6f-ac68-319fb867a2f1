-- =====================================================
-- InnoBook Database Encryption Setup Script
-- =====================================================
-- This script sets up the required PostgreSQL extensions
-- and functions for the InnoBook encryption system.
--
-- Run this script as a PostgreSQL superuser or database owner
-- before using the encryption features.
-- =====================================================

-- Enable the pgcrypto extension (required for encryption functions)
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Verify the extension is installed
SELECT 
    extname as "Extension Name",
    extversion as "Version",
    CASE 
        WHEN extname = 'pgcrypto' THEN '✅ Required for encryption'
        ELSE 'ℹ️  Other extension'
    END as "Status"
FROM pg_extension 
WHERE extname = 'pgcrypto';

-- Test the encryption functions
DO $$
DECLARE
    test_text TEXT := 'Hello World';
    test_key TEXT := 'test-encryption-key-32-characters';
    encrypted_data BYTEA;
    decrypted_text TEXT;
BEGIN
    -- Test encryption
    SELECT pgp_sym_encrypt(test_text, test_key) INTO encrypted_data;
    
    -- Test decryption
    SELECT pgp_sym_decrypt(encrypted_data, test_key) INTO decrypted_text;
    
    -- Verify the round-trip works
    IF decrypted_text = test_text THEN
        RAISE NOTICE '✅ Encryption test PASSED: % -> encrypted -> %', test_text, decrypted_text;
    ELSE
        RAISE EXCEPTION '❌ Encryption test FAILED: Expected %, got %', test_text, decrypted_text;
    END IF;
END $$;

-- Display available pgcrypto functions
SELECT 
    proname as "Function Name",
    pg_get_function_identity_arguments(oid) as "Arguments",
    CASE 
        WHEN proname LIKE 'pgp_sym_%' THEN '🔐 Symmetric encryption'
        WHEN proname LIKE 'pgp_pub_%' THEN '🔑 Public key encryption'
        WHEN proname LIKE 'digest%' OR proname LIKE 'hmac%' THEN '🔒 Hashing'
        WHEN proname LIKE 'gen_%' THEN '🎲 Random generation'
        ELSE '🔧 Other crypto function'
    END as "Category"
FROM pg_proc 
WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
  AND proname ~ '^(pgp_|digest|hmac|gen_|crypt|decrypt|encrypt)'
ORDER BY proname;

-- Show encryption configuration recommendations
SELECT 
    'Database Setup' as "Setup Step",
    'Complete ✅' as "Status",
    'pgcrypto extension is now available' as "Details"
UNION ALL
SELECT 
    'Next Steps',
    'Action Required ⚠️',
    'Set ENCRYPTION_KEY in appsettings.json (32+ characters)'
UNION ALL
SELECT 
    'Security Note',
    'Important 🔒',
    'Store encryption key securely, never commit to source control'
UNION ALL
SELECT 
    'Testing',
    'Recommended 🧪',
    'Use GET /api/Users/<USER>';

-- Final success message
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎉 Database encryption setup completed successfully!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 Next steps:';
    RAISE NOTICE '   1. Set ENCRYPTION_KEY in your appsettings.json';
    RAISE NOTICE '   2. Test encryption: GET /api/Users/<USER>';
    RAISE NOTICE '   3. Run migration: Scripts/migrate-encryption.bat --dry-run';
    RAISE NOTICE '';
END $$;
