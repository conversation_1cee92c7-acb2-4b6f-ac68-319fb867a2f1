using InnoBook.Common;
using InnoBook.Entities;
using InnoBook.Services.Interface;
using Microsoft.EntityFrameworkCore;

namespace InnoLogiciel.Server.Contexts
{
    /// <summary>
    /// Extended DbContext that automatically handles encryption/decryption
    /// </summary>
    public class EncryptedInnoLogicielContext : InnoLogicielContext
    {
        private readonly IEncryptionService _encryptionService;

        public EncryptedInnoLogicielContext(DbContextOptions<InnoLogicielContext> options, IEncryptionService encryptionService)
            : base(options)
        {
            _encryptionService = encryptionService;
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            await EncryptEntitiesBeforeSaving();
            var result = await base.SaveChangesAsync(cancellationToken);
            await DecryptEntitiesAfterSaving();
            return result;
        }

        public override int SaveChanges()
        {
            EncryptEntitiesBeforeSaving().GetAwaiter().GetResult();
            var result = base.SaveChanges();
            DecryptEntitiesAfterSaving().GetAwaiter().GetResult();
            return result;
        }

        private async Task EncryptEntitiesBeforeSaving()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified)
                .Where(e => IsEncryptableEntity(e.Entity));

            foreach (var entry in entries)
            {
                await EncryptEntity(entry.Entity);
            }
        }

        private async Task DecryptEntitiesAfterSaving()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Unchanged || e.State == EntityState.Modified)
                .Where(e => IsEncryptableEntity(e.Entity));

            foreach (var entry in entries)
            {
                await DecryptEntity(entry.Entity);
            }
        }

        private bool IsEncryptableEntity(object entity)
        {
            return entity is User || entity is Client || entity is Company;
        }

        private async Task EncryptEntity(object entity)
        {
            switch (entity)
            {
                case User user:
                    await EncryptUserData(user);
                    break;
                case Client client:
                    await EncryptClientData(client);
                    break;
                case Company company:
                    await EncryptCompanyData(company);
                    break;
            }
        }

        private async Task DecryptEntity(object entity)
        {
            switch (entity)
            {
                case User user:
                    await DecryptUserData(user);
                    break;
                case Client client:
                    await DecryptClientData(client);
                    break;
                case Company company:
                    await DecryptCompanyData(company);
                    break;
            }
        }

        private async Task EncryptUserData(User user)
        {
            if (!string.IsNullOrEmpty(user.FirstName) && !EncryptionHelper.IsLikelyEncryptedData(user.FirstName))
            {
                user.FirstName = await _encryptionService.EncryptAsync(user.FirstName);
            }

            if (!string.IsNullOrEmpty(user.LastName) && !EncryptionHelper.IsLikelyEncryptedData(user.LastName))
            {
                user.LastName = await _encryptionService.EncryptAsync(user.LastName);
            }
        }

        private async Task DecryptUserData(User user)
        {
            if (!string.IsNullOrEmpty(user.FirstName) && EncryptionHelper.IsLikelyEncryptedData(user.FirstName))
            {
                user.FirstName = await _encryptionService.DecryptAsync(user.FirstName);
            }

            if (!string.IsNullOrEmpty(user.LastName) && EncryptionHelper.IsLikelyEncryptedData(user.LastName))
            {
                user.LastName = await _encryptionService.DecryptAsync(user.LastName);
            }
        }

        private async Task EncryptClientData(Client client)
        {
            if (!string.IsNullOrEmpty(client.FirstName) && !EncryptionHelper.IsLikelyEncryptedData(client.FirstName))
            {
                client.FirstName = await _encryptionService.EncryptAsync(client.FirstName);
            }

            if (!string.IsNullOrEmpty(client.LastName) && !EncryptionHelper.IsLikelyEncryptedData(client.LastName))
            {
                client.LastName = await _encryptionService.EncryptAsync(client.LastName);
            }

            if (!string.IsNullOrEmpty(client.PhoneNumber) && !EncryptionHelper.IsLikelyEncryptedData(client.PhoneNumber))
            {
                client.PhoneNumber = await _encryptionService.EncryptAsync(client.PhoneNumber);
            }

            if (!string.IsNullOrEmpty(client.BusinessPhoneNumber) && !EncryptionHelper.IsLikelyEncryptedData(client.BusinessPhoneNumber))
            {
                client.BusinessPhoneNumber = await _encryptionService.EncryptAsync(client.BusinessPhoneNumber);
            }

            if (!string.IsNullOrEmpty(client.MobilePhoneNumber) && !EncryptionHelper.IsLikelyEncryptedData(client.MobilePhoneNumber))
            {
                client.MobilePhoneNumber = await _encryptionService.EncryptAsync(client.MobilePhoneNumber);
            }

            if (!string.IsNullOrEmpty(client.AddressLine1) && !EncryptionHelper.IsLikelyEncryptedData(client.AddressLine1))
            {
                client.AddressLine1 = await _encryptionService.EncryptAsync(client.AddressLine1);
            }

            if (!string.IsNullOrEmpty(client.AddressLine2) && !EncryptionHelper.IsLikelyEncryptedData(client.AddressLine2))
            {
                client.AddressLine2 = await _encryptionService.EncryptAsync(client.AddressLine2);
            }
        }

        private async Task DecryptClientData(Client client)
        {
            if (!string.IsNullOrEmpty(client.FirstName) && EncryptionHelper.IsLikelyEncryptedData(client.FirstName))
            {
                client.FirstName = await _encryptionService.DecryptAsync(client.FirstName);
            }

            if (!string.IsNullOrEmpty(client.LastName) && EncryptionHelper.IsLikelyEncryptedData(client.LastName))
            {
                client.LastName = await _encryptionService.DecryptAsync(client.LastName);
            }

            if (!string.IsNullOrEmpty(client.PhoneNumber) && EncryptionHelper.IsLikelyEncryptedData(client.PhoneNumber))
            {
                client.PhoneNumber = await _encryptionService.DecryptAsync(client.PhoneNumber);
            }

            if (!string.IsNullOrEmpty(client.BusinessPhoneNumber) && EncryptionHelper.IsLikelyEncryptedData(client.BusinessPhoneNumber))
            {
                client.BusinessPhoneNumber = await _encryptionService.DecryptAsync(client.BusinessPhoneNumber);
            }

            if (!string.IsNullOrEmpty(client.MobilePhoneNumber) && EncryptionHelper.IsLikelyEncryptedData(client.MobilePhoneNumber))
            {
                client.MobilePhoneNumber = await _encryptionService.DecryptAsync(client.MobilePhoneNumber);
            }

            if (!string.IsNullOrEmpty(client.AddressLine1) && EncryptionHelper.IsLikelyEncryptedData(client.AddressLine1))
            {
                client.AddressLine1 = await _encryptionService.DecryptAsync(client.AddressLine1);
            }

            if (!string.IsNullOrEmpty(client.AddressLine2) && EncryptionHelper.IsLikelyEncryptedData(client.AddressLine2))
            {
                client.AddressLine2 = await _encryptionService.DecryptAsync(client.AddressLine2);
            }
        }

        private async Task EncryptCompanyData(Company company)
        {
            if (!string.IsNullOrEmpty(company.Phone) && !EncryptionHelper.IsLikelyEncryptedData(company.Phone))
            {
                company.Phone = await _encryptionService.EncryptAsync(company.Phone);
            }

            if (!string.IsNullOrEmpty(company.Adress) && !EncryptionHelper.IsLikelyEncryptedData(company.Adress))
            {
                company.Adress = await _encryptionService.EncryptAsync(company.Adress);
            }

            if (!string.IsNullOrEmpty(company.Adress2) && !EncryptionHelper.IsLikelyEncryptedData(company.Adress2))
            {
                company.Adress2 = await _encryptionService.EncryptAsync(company.Adress2);
            }
        }

        private async Task DecryptCompanyData(Company company)
        {
            if (!string.IsNullOrEmpty(company.Phone) && EncryptionHelper.IsLikelyEncryptedData(company.Phone))
            {
                company.Phone = await _encryptionService.DecryptAsync(company.Phone);
            }

            if (!string.IsNullOrEmpty(company.Adress) && EncryptionHelper.IsLikelyEncryptedData(company.Adress))
            {
                company.Adress = await _encryptionService.DecryptAsync(company.Adress);
            }

            if (!string.IsNullOrEmpty(company.Adress2) && EncryptionHelper.IsLikelyEncryptedData(company.Adress2))
            {
                company.Adress2 = await _encryptionService.DecryptAsync(company.Adress2);
            }
        }
    }
}
