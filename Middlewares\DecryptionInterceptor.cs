using InnoBook.Common;
using InnoBook.Entities;
using InnoBook.Services.Interface;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;

namespace InnoBook.Middlewares
{
    /// <summary>
    /// Interceptor that handles decryption during entity materialization
    /// </summary>
    public class EntityMaterializationInterceptor : IMaterializationInterceptor
    {
        private readonly IEncryptionService _encryptionService;

        public EntityMaterializationInterceptor(IEncryptionService encryptionService)
        {
            _encryptionService = encryptionService;
        }

        public object InitializedInstance(MaterializationInterceptionData materializationData, object instance)
        {
            // Decrypt the entity after it's been materialized from the database
            if (IsEncryptableEntity(instance))
            {
                // We need to decrypt asynchronously, but this method is synchronous
                // We'll use a task and wait for it (not ideal, but necessary for this interceptor)
                DecryptEntityAsync(instance).GetAwaiter().GetResult();
            }

            return instance;
        }

        private bool IsEncryptableEntity(object entity)
        {
            return entity is User || entity is Client || entity is Company;
        }

        private async Task DecryptEntityAsync(object entity)
        {
            try
            {
                switch (entity)
                {
                    case User user:
                        await DecryptUserData(user);
                        break;
                    case Client client:
                        await DecryptClientData(client);
                        break;
                    case Company company:
                        await DecryptCompanyData(company);
                        break;
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't throw to avoid breaking the application
                Console.WriteLine($"Decryption error for {entity.GetType().Name}: {ex.Message}");
            }
        }

        private async Task DecryptUserData(User user)
        {
            if (!string.IsNullOrEmpty(user.FirstName) && EncryptionHelper.IsLikelyEncryptedData(user.FirstName))
            {
                user.FirstName = await _encryptionService.DecryptAsync(user.FirstName);
            }

            if (!string.IsNullOrEmpty(user.LastName) && EncryptionHelper.IsLikelyEncryptedData(user.LastName))
            {
                user.LastName = await _encryptionService.DecryptAsync(user.LastName);
            }
        }

        private async Task DecryptClientData(Client client)
        {
            if (!string.IsNullOrEmpty(client.FirstName) && EncryptionHelper.IsLikelyEncryptedData(client.FirstName))
            {
                client.FirstName = await _encryptionService.DecryptAsync(client.FirstName);
            }

            if (!string.IsNullOrEmpty(client.LastName) && EncryptionHelper.IsLikelyEncryptedData(client.LastName))
            {
                client.LastName = await _encryptionService.DecryptAsync(client.LastName);
            }

            if (!string.IsNullOrEmpty(client.PhoneNumber) && EncryptionHelper.IsLikelyEncryptedData(client.PhoneNumber))
            {
                client.PhoneNumber = await _encryptionService.DecryptAsync(client.PhoneNumber);
            }

            if (!string.IsNullOrEmpty(client.BusinessPhoneNumber) && EncryptionHelper.IsLikelyEncryptedData(client.BusinessPhoneNumber))
            {
                client.BusinessPhoneNumber = await _encryptionService.DecryptAsync(client.BusinessPhoneNumber);
            }

            if (!string.IsNullOrEmpty(client.MobilePhoneNumber) && EncryptionHelper.IsLikelyEncryptedData(client.MobilePhoneNumber))
            {
                client.MobilePhoneNumber = await _encryptionService.DecryptAsync(client.MobilePhoneNumber);
            }

            if (!string.IsNullOrEmpty(client.AddressLine1) && EncryptionHelper.IsLikelyEncryptedData(client.AddressLine1))
            {
                client.AddressLine1 = await _encryptionService.DecryptAsync(client.AddressLine1);
            }

            if (!string.IsNullOrEmpty(client.AddressLine2) && EncryptionHelper.IsLikelyEncryptedData(client.AddressLine2))
            {
                client.AddressLine2 = await _encryptionService.DecryptAsync(client.AddressLine2);
            }
        }

        private async Task DecryptCompanyData(Company company)
        {
            if (!string.IsNullOrEmpty(company.Phone) && EncryptionHelper.IsLikelyEncryptedData(company.Phone))
            {
                company.Phone = await _encryptionService.DecryptAsync(company.Phone);
            }

            if (!string.IsNullOrEmpty(company.Adress) && EncryptionHelper.IsLikelyEncryptedData(company.Adress))
            {
                company.Adress = await _encryptionService.DecryptAsync(company.Adress);
            }

            if (!string.IsNullOrEmpty(company.Adress2) && EncryptionHelper.IsLikelyEncryptedData(company.Adress2))
            {
                company.Adress2 = await _encryptionService.DecryptAsync(company.Adress2);
            }
        }
    }
}
