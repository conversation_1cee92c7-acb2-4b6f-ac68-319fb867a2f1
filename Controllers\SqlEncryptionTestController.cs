using InnoBook.Entities;
using InnoLogiciel.Server.Contexts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace InnoBook.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SqlEncryptionTestController : ControllerBase
    {
        private readonly InnoLogicielContext _context;

        public SqlEncryptionTestController(InnoLogicielContext context)
        {
            _context = context;
        }

        /// <summary>
        /// Test SQL-level encryption by creating a user and showing the generated SQL
        /// </summary>
        [HttpPost("test-insert")]
        public async Task<IActionResult> TestInsert([FromBody] TestUserRequest request)
        {
            try
            {
                var user = new User
                {
                    Id = Guid.NewGuid(),
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    Email = request.Email,
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedAt = DateTime.UtcNow
                };

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    Message = "User created successfully with SQL-level encryption",
                    UserId = user.Id,
                    Note = "Check the console output to see the modified SQL queries"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        /// <summary>
        /// Test SQL-level decryption by retrieving users
        /// </summary>
        [HttpGet("test-select")]
        public async Task<IActionResult> TestSelect()
        {
            try
            {
                var users = await _context.Users
                    .Take(5)
                    .Select(u => new
                    {
                        u.Id,
                        u.FirstName,
                        u.LastName,
                        u.Email
                    })
                    .ToListAsync();

                return Ok(new
                {
                    Message = "Users retrieved with SQL-level decryption",
                    Users = users,
                    Note = "Check the console output to see the modified SQL queries"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        /// <summary>
        /// Test SQL-level encryption for updates
        /// </summary>
        [HttpPut("test-update/{id}")]
        public async Task<IActionResult> TestUpdate(Guid id, [FromBody] TestUserRequest request)
        {
            try
            {
                var user = await _context.Users.FindAsync(id);
                if (user == null)
                {
                    return NotFound("User not found");
                }

                user.FirstName = request.FirstName;
                user.LastName = request.LastName;
                user.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    Message = "User updated successfully with SQL-level encryption",
                    UserId = user.Id,
                    Note = "Check the console output to see the modified SQL queries"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        /// <summary>
        /// Test client creation with multiple encrypted fields
        /// </summary>
        [HttpPost("test-client")]
        public async Task<IActionResult> TestClient([FromBody] TestClientRequest request)
        {
            try
            {
                var client = new Client
                {
                    Id = Guid.NewGuid(),
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    PhoneNumber = request.PhoneNumber,
                    BusinessPhoneNumber = request.BusinessPhoneNumber,
                    MobilePhoneNumber = request.MobilePhoneNumber,
                    AddressLine1 = request.AddressLine1,
                    AddressLine2 = request.AddressLine2,
                    CreatedBy = Guid.NewGuid().ToString(),
                    CreatedAt = DateTime.UtcNow
                };

                _context.Clients.Add(client);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    Message = "Client created successfully with SQL-level encryption",
                    ClientId = client.Id,
                    Note = "Check the console output to see the modified SQL queries with multiple encrypted fields"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Error = ex.Message });
            }
        }

        /// <summary>
        /// Show raw SQL that would be generated for comparison
        /// </summary>
        [HttpGet("show-raw-sql")]
        public IActionResult ShowRawSql()
        {
            var examples = new
            {
                OriginalInsert = "INSERT INTO users (id, first_name, last_name, email) VALUES (@p0, @p1, @p2, @p3)",
                ModifiedInsert = "INSERT INTO users (id, first_name, last_name, email) VALUES (@p0, pgp_sym_encrypt(@p1::text, 'encryption_key'), pgp_sym_encrypt(@p2::text, 'encryption_key'), @p3)",
                
                OriginalSelect = "SELECT u.first_name, u.last_name, u.email FROM users u",
                ModifiedSelect = "SELECT CASE WHEN u.first_name IS NOT NULL AND LENGTH(u.first_name) > 50 THEN pgp_sym_decrypt(u.first_name::bytea, 'encryption_key') ELSE u.first_name END, CASE WHEN u.last_name IS NOT NULL AND LENGTH(u.last_name) > 50 THEN pgp_sym_decrypt(u.last_name::bytea, 'encryption_key') ELSE u.last_name END, u.email FROM users u",
                
                OriginalUpdate = "UPDATE users SET first_name = @p0, last_name = @p1 WHERE id = @p2",
                ModifiedUpdate = "UPDATE users SET first_name = pgp_sym_encrypt(@p0::text, 'encryption_key'), last_name = pgp_sym_encrypt(@p1::text, 'encryption_key') WHERE id = @p2"
            };

            return Ok(new
            {
                Message = "Examples of SQL query modifications for encryption",
                Examples = examples,
                Note = "The actual encryption key is not shown here for security reasons"
            });
        }
    }

    public class TestUserRequest
    {
        public string FirstName { get; set; } = "";
        public string LastName { get; set; } = "";
        public string Email { get; set; } = "";
    }

    public class TestClientRequest
    {
        public string FirstName { get; set; } = "";
        public string LastName { get; set; } = "";
        public string PhoneNumber { get; set; } = "";
        public string BusinessPhoneNumber { get; set; } = "";
        public string MobilePhoneNumber { get; set; } = "";
        public string AddressLine1 { get; set; } = "";
        public string AddressLine2 { get; set; } = "";
        public string Email { get; set; } = "";
    }
}
