@echo off
setlocal enabledelayedexpansion

echo ===============================================
echo   InnoBook Database Encryption Setup
echo ===============================================
echo.

REM Check if we're in the right directory
if not exist "InnoBook.sln" (
    echo ERROR: Please run this script from the InnoBook project root directory
    pause
    exit /b 1
)

REM Check if psql is available
psql --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: PostgreSQL client (psql) not found
    echo Please install PostgreSQL client tools from:
    echo https://www.postgresql.org/download/
    echo.
    pause
    exit /b 1
)

echo PostgreSQL client found.
echo.

REM Get database connection details
echo Please provide your PostgreSQL connection details:
echo.

set /p "DB_HOST=Database Host (default: localhost): "
if "%DB_HOST%"=="" set DB_HOST=localhost

set /p "DB_PORT=Database Port (default: 5432): "
if "%DB_PORT%"=="" set DB_PORT=5432

set /p "DB_NAME=Database Name: "
if "%DB_NAME%"=="" (
    echo ERROR: Database name is required
    pause
    exit /b 1
)

set /p "DB_USER=Username: "
if "%DB_USER%"=="" (
    echo ERROR: Username is required
    pause
    exit /b 1
)

echo.
echo Connecting to: %DB_HOST%:%DB_PORT%/%DB_NAME% as %DB_USER%
echo.

REM Test connection first
echo Testing database connection...
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -c "SELECT version();" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Failed to connect to database
    echo Please check your connection details and try again.
    pause
    exit /b 1
)

echo Connection successful!
echo.

REM Run the setup script
echo Setting up encryption extensions...
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -f Scripts\setup-database-encryption.sql

if errorlevel 1 (
    echo ERROR: Failed to run setup script
    pause
    exit /b 1
)

echo.
echo ===============================================
echo   Setup Completed Successfully!
echo ===============================================
echo.
echo Next steps:
echo 1. Verify encryption key is set in appsettings.json
echo 2. Test encryption: GET /api/Users/<USER>
echo 3. Run migration preview: migrate-encryption.bat --dry-run
echo 4. Run actual migration: migrate-encryption.bat
echo.
echo Security reminder:
echo - Keep your encryption key secure and backed up
echo - Never commit encryption keys to source control
echo - Use environment variables in production
echo.
pause
