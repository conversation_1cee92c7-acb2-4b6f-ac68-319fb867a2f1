using System.Text.RegularExpressions;

namespace InnoBook.Common
{
    /// <summary>
    /// Helper class for encryption-related utilities
    /// </summary>
    public static class EncryptionHelper
    {
        /// <summary>
        /// Validates if a string is a valid Base64 encoded string
        /// </summary>
        /// <param name="base64String">The string to validate</param>
        /// <returns>True if the string is valid Base64, false otherwise</returns>
        public static bool IsValidBase64String(string? base64String)
        {
            if (string.IsNullOrEmpty(base64String))
                return false;

            // Base64 strings should have length that's a multiple of 4
            if (base64String.Length % 4 != 0)
                return false;

            // Base64 strings should only contain valid Base64 characters
            // Valid characters: A-Z, a-z, 0-9, +, /, and = for padding
            if (!Regex.IsMatch(base64String, @"^[A-Za-z0-9+/]*={0,2}$"))
                return false;

            try
            {
                // Try to convert from Base64 - if it fails, it's not valid Base64
                Convert.FromBase64String(base64String);
                return true;
            }
            catch (FormatException)
            {
                return false;
            }
            catch (ArgumentException)
            {
                return false;
            }
        }

        /// <summary>
        /// Checks if a string is likely to be encrypted data (valid Base64 with reasonable length)
        /// </summary>
        /// <param name="data">The string to check</param>
        /// <param name="minLength">Minimum length to consider as encrypted (default: 20)</param>
        /// <returns>True if the string appears to be encrypted data</returns>
        public static bool IsLikelyEncryptedData(string? data, int minLength = 20)
        {
            if (string.IsNullOrEmpty(data))
                return false;

            // Check if it's a valid Base64 string that's likely to be encrypted data
            return IsValidBase64String(data) && data.Length > minLength;
        }

        /// <summary>
        /// Safely attempts to decode a Base64 string
        /// </summary>
        /// <param name="base64String">The Base64 string to decode</param>
        /// <param name="result">The decoded bytes if successful</param>
        /// <returns>True if decoding was successful, false otherwise</returns>
        public static bool TryDecodeBase64(string? base64String, out byte[]? result)
        {
            result = null;

            if (!IsValidBase64String(base64String))
                return false;

            try
            {
                result = Convert.FromBase64String(base64String!);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Safely encodes bytes to a Base64 string
        /// </summary>
        /// <param name="bytes">The bytes to encode</param>
        /// <returns>Base64 encoded string, or null if input is null</returns>
        public static string? EncodeToBase64(byte[]? bytes)
        {
            if (bytes == null)
                return null;

            try
            {
                return Convert.ToBase64String(bytes);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Checks if a string contains only printable ASCII characters (likely plain text)
        /// </summary>
        /// <param name="text">The string to check</param>
        /// <returns>True if the string contains only printable ASCII characters</returns>
        public static bool IsLikelyPlainText(string? text)
        {
            if (string.IsNullOrEmpty(text))
                return true;

            // Check if all characters are printable ASCII (32-126) or common whitespace
            return text.All(c => (c >= 32 && c <= 126) || c == '\t' || c == '\n' || c == '\r');
        }

        /// <summary>
        /// Determines the most likely format of a string (Plain Text, Base64, or Unknown)
        /// </summary>
        /// <param name="data">The string to analyze</param>
        /// <returns>The detected format</returns>
        public static DataFormat DetectDataFormat(string? data)
        {
            if (string.IsNullOrEmpty(data))
                return DataFormat.PlainText;

            if (IsValidBase64String(data) && data.Length > 20)
                return DataFormat.Base64;

            if (IsLikelyPlainText(data))
                return DataFormat.PlainText;

            return DataFormat.Unknown;
        }
    }

    /// <summary>
    /// Enumeration of possible data formats
    /// </summary>
    public enum DataFormat
    {
        PlainText,
        Base64,
        Unknown
    }
}
