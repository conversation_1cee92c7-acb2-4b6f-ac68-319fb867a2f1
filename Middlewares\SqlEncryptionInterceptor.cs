using Microsoft.EntityFrameworkCore.Diagnostics;
using System.Data.Common;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Configuration;

namespace InnoBook.Middlewares
{
    /// <summary>
    /// Interceptor that modifies SQL queries to automatically encrypt/decrypt sensitive fields
    /// </summary>
    public class SqlEncryptionInterceptor : DbCommandInterceptor
    {
        private readonly string _encryptionKey;
        
        // Define which tables and columns should be encrypted
        private readonly Dictionary<string, List<string>> _encryptedFields = new()
        {
            ["users"] = new() { "first_name", "last_name" },
            ["clients"] = new() { "first_name", "last_name", "phone_number", "business_phone_number", "mobile_phone_number", "address_line1", "address_line2" },
            ["companies"] = new() { "phone", "adress", "adress2" }
        };

        public SqlEncryptionInterceptor(IConfiguration configuration)
        {
            _encryptionKey = configuration["ENCRYPTION_KEY"] ?? throw new InvalidOperationException("Encryption key not configured");
        }

        public override InterceptionResult<DbDataReader> ReaderExecuting(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<DbDataReader> result)
        {
            ModifyCommandForEncryption(command);
            return base.ReaderExecuting(command, eventData, result);
        }

        public override ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<DbDataReader> result,
            CancellationToken cancellationToken = default)
        {
            ModifyCommandForEncryption(command);
            return base.ReaderExecutingAsync(command, eventData, result, cancellationToken);
        }

        public override InterceptionResult<int> NonQueryExecuting(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<int> result)
        {
            ModifyCommandForEncryption(command);
            return base.NonQueryExecuting(command, eventData, result);
        }

        public override ValueTask<InterceptionResult<int>> NonQueryExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<int> result,
            CancellationToken cancellationToken = default)
        {
            ModifyCommandForEncryption(command);
            return base.NonQueryExecutingAsync(command, eventData, result, cancellationToken);
        }

        private void ModifyCommandForEncryption(DbCommand command)
        {
            if (string.IsNullOrEmpty(command.CommandText))
                return;

            try
            {
                var originalSql = command.CommandText;
                var modifiedSql = originalSql;

                // Handle different types of SQL operations
                if (IsSelectQuery(originalSql))
                {
                    modifiedSql = ModifySelectQuery(originalSql);
                }
                else if (IsInsertQuery(originalSql))
                {
                    modifiedSql = ModifyInsertQuery(originalSql);
                }
                else if (IsUpdateQuery(originalSql))
                {
                    modifiedSql = ModifyUpdateQuery(originalSql);
                }

                if (modifiedSql != originalSql)
                {
                    command.CommandText = modifiedSql;
                    
                    // Debug logging (remove in production)
                    Console.WriteLine($"Original SQL: {originalSql}");
                    Console.WriteLine($"Modified SQL: {modifiedSql}");
                }
            }
            catch (Exception ex)
            {
                // Log error but don't break the query
                Console.WriteLine($"Error modifying SQL for encryption: {ex.Message}");
            }
        }

        private bool IsSelectQuery(string sql)
        {
            return sql.TrimStart().StartsWith("SELECT", StringComparison.OrdinalIgnoreCase);
        }

        private bool IsInsertQuery(string sql)
        {
            return sql.TrimStart().StartsWith("INSERT", StringComparison.OrdinalIgnoreCase);
        }

        private bool IsUpdateQuery(string sql)
        {
            return sql.TrimStart().StartsWith("UPDATE", StringComparison.OrdinalIgnoreCase);
        }

        private string ModifySelectQuery(string sql)
        {
            var modifiedSql = sql;

            foreach (var table in _encryptedFields.Keys)
            {
                // Check if this table is referenced in the query
                if (!ContainsTable(sql, table))
                    continue;

                foreach (var column in _encryptedFields[table])
                {
                    // Handle different SELECT patterns

                    // Pattern 1: table_alias.column_name
                    var aliasPattern = $@"\b(\w+)\.{column}\b";
                    var aliasMatches = Regex.Matches(modifiedSql, aliasPattern, RegexOptions.IgnoreCase);

                    foreach (Match match in aliasMatches)
                    {
                        var alias = match.Groups[1].Value;
                        var originalMatch = match.Value;

                        // Check if this alias refers to our table
                        if (IsTableAlias(sql, alias, table))
                        {
                            var replacement = $"CASE WHEN {alias}.{column} IS NOT NULL AND LENGTH({alias}.{column}) > 50 " +
                                            $"THEN pgp_sym_decrypt({alias}.{column}::bytea, '{_encryptionKey}') " +
                                            $"ELSE {alias}.{column} END";
                            modifiedSql = modifiedSql.Replace(originalMatch, replacement);
                        }
                    }

                    // Pattern 2: Direct column reference (when table name is clear from context)
                    var directPattern = $@"\bSELECT\s+.*?\b{column}\b";
                    if (Regex.IsMatch(modifiedSql, directPattern, RegexOptions.IgnoreCase))
                    {
                        // Only replace if we can determine the table context
                        var tableAlias = GetTableAliasFromQuery(sql, table);
                        if (!string.IsNullOrEmpty(tableAlias))
                        {
                            var directColumnPattern = $@"\b{column}\b(?=\s*(?:,|\s+FROM|\s+WHERE|\s+ORDER|\s+GROUP|\s*$))";
                            var replacement = $"CASE WHEN {tableAlias}.{column} IS NOT NULL AND LENGTH({tableAlias}.{column}) > 50 " +
                                            $"THEN pgp_sym_decrypt({tableAlias}.{column}::bytea, '{_encryptionKey}') " +
                                            $"ELSE {tableAlias}.{column} END AS {column}";
                            modifiedSql = Regex.Replace(modifiedSql, directColumnPattern, replacement, RegexOptions.IgnoreCase);
                        }
                    }
                }
            }

            return modifiedSql;
        }

        private bool ContainsTable(string sql, string tableName)
        {
            // Check for table name in FROM clause
            var fromPattern = $@"\bFROM\s+{tableName}\b";
            return Regex.IsMatch(sql, fromPattern, RegexOptions.IgnoreCase);
        }

        private bool IsTableAlias(string sql, string alias, string tableName)
        {
            // Check if alias is defined for the table
            var aliasPattern = $@"\bFROM\s+{tableName}\s+(?:AS\s+)?{alias}\b";
            return Regex.IsMatch(sql, aliasPattern, RegexOptions.IgnoreCase);
        }

        private string GetTableAliasFromQuery(string sql, string tableName)
        {
            // Extract table alias from FROM clause
            var aliasPattern = $@"\bFROM\s+{tableName}\s+(?:AS\s+)?(\w+)\b";
            var match = Regex.Match(sql, aliasPattern, RegexOptions.IgnoreCase);

            if (match.Success)
            {
                return match.Groups[1].Value;
            }

            // If no alias, return the table name itself
            return tableName;
        }

        private string ModifyInsertQuery(string sql)
        {
            foreach (var table in _encryptedFields.Keys)
            {
                if (!sql.Contains(table, StringComparison.OrdinalIgnoreCase))
                    continue;

                // Handle different INSERT patterns that Entity Framework generates

                // Pattern 1: INSERT INTO table (columns) VALUES (values)
                var insertPattern = $@"INSERT\s+INTO\s+{table}\s*\(([^)]+)\)\s*VALUES\s*\(([^)]+)\)";
                var match = Regex.Match(sql, insertPattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);

                if (match.Success)
                {
                    sql = ProcessInsertMatch(sql, match, table);
                }
                else
                {
                    // Pattern 2: INSERT INTO table (columns) OUTPUT ... VALUES (values)
                    var insertOutputPattern = $@"INSERT\s+INTO\s+{table}\s*\(([^)]+)\)\s*OUTPUT[^V]*VALUES\s*\(([^)]+)\)";
                    var outputMatch = Regex.Match(sql, insertOutputPattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);

                    if (outputMatch.Success)
                    {
                        sql = ProcessInsertWithOutput(sql, outputMatch, table);
                    }
                }
            }

            return sql;
        }

        private string ProcessInsertMatch(string sql, Match match, string table)
        {
            var columnsText = match.Groups[1].Value;
            var valuesText = match.Groups[2].Value;

            var columns = columnsText.Split(',').Select(c => c.Trim().Trim('"', '`', '[')).ToArray();
            var values = SplitValues(valuesText);

            var modifiedValues = new List<string>();

            for (int i = 0; i < columns.Length && i < values.Count; i++)
            {
                var column = columns[i].ToLower().Replace("]", ""); // Handle [column_name] format
                var value = values[i].Trim();

                if (_encryptedFields[table].Contains(column))
                {
                    modifiedValues.Add(WrapWithEncryption(value));
                }
                else
                {
                    modifiedValues.Add(value);
                }
            }

            var newValuesClause = string.Join(", ", modifiedValues);
            return sql.Replace(match.Groups[2].Value, newValuesClause);
        }

        private string ProcessInsertWithOutput(string sql, Match match, string table)
        {
            // Similar to ProcessInsertMatch but handles OUTPUT clause
            var columnsText = match.Groups[1].Value;
            var valuesText = match.Groups[2].Value;

            var columns = columnsText.Split(',').Select(c => c.Trim().Trim('"', '`', '[', ']')).ToArray();
            var values = SplitValues(valuesText);

            var modifiedValues = new List<string>();

            for (int i = 0; i < columns.Length && i < values.Count; i++)
            {
                var column = columns[i].ToLower();
                var value = values[i].Trim();

                if (_encryptedFields[table].Contains(column))
                {
                    modifiedValues.Add(WrapWithEncryption(value));
                }
                else
                {
                    modifiedValues.Add(value);
                }
            }

            var newValuesClause = string.Join(", ", modifiedValues);
            return sql.Replace(match.Groups[2].Value, newValuesClause);
        }

        private string WrapWithEncryption(string value)
        {
            // Handle different value types
            if (value.StartsWith("@") || value.StartsWith("$"))
            {
                // Parameter - wrap with encryption, handle NULL case
                return $"CASE WHEN {value} IS NULL THEN NULL ELSE pgp_sym_encrypt({value}::text, '{_encryptionKey}') END";
            }
            else if (value.Equals("NULL", StringComparison.OrdinalIgnoreCase))
            {
                // NULL value - keep as is
                return value;
            }
            else if (value.StartsWith("'") && value.EndsWith("'"))
            {
                // String literal - encrypt directly
                return $"pgp_sym_encrypt({value}, '{_encryptionKey}')";
            }
            else
            {
                // Other cases - try to encrypt with NULL check
                return $"CASE WHEN {value} IS NULL THEN NULL ELSE pgp_sym_encrypt({value}::text, '{_encryptionKey}') END";
            }
        }

        private string ModifyUpdateQuery(string sql)
        {
            foreach (var table in _encryptedFields.Keys)
            {
                if (!sql.Contains(table, StringComparison.OrdinalIgnoreCase))
                    continue;

                foreach (var column in _encryptedFields[table])
                {
                    // Handle different UPDATE patterns

                    // Pattern 1: SET column = value
                    var setPattern = $@"\bSET\s+([^=]*\b{column}\s*=\s*)([^,\r\n]+)";
                    var matches = Regex.Matches(sql, setPattern, RegexOptions.IgnoreCase);

                    foreach (Match match in matches)
                    {
                        var setClause = match.Groups[1].Value;
                        var value = match.Groups[2].Value.Trim();

                        var encryptedValue = WrapWithEncryption(value);
                        var newSetClause = setClause + encryptedValue;

                        sql = sql.Replace(match.Value, newSetClause);
                    }

                    // Pattern 2: SET [column] = value (with brackets)
                    var setBracketPattern = $@"\bSET\s+([^=]*\[{column}\]\s*=\s*)([^,\r\n]+)";
                    var bracketMatches = Regex.Matches(sql, setBracketPattern, RegexOptions.IgnoreCase);

                    foreach (Match match in bracketMatches)
                    {
                        var setClause = match.Groups[1].Value;
                        var value = match.Groups[2].Value.Trim();

                        var encryptedValue = WrapWithEncryption(value);
                        var newSetClause = setClause + encryptedValue;

                        sql = sql.Replace(match.Value, newSetClause);
                    }

                    // Pattern 3: Multiple SET clauses
                    var multiSetPattern = $@"({column}\s*=\s*)([^,\r\n]+)(?=\s*,|\s*WHERE|\s*$)";
                    var multiMatches = Regex.Matches(sql, multiSetPattern, RegexOptions.IgnoreCase);

                    foreach (Match match in multiMatches)
                    {
                        var columnPart = match.Groups[1].Value;
                        var value = match.Groups[2].Value.Trim();

                        var encryptedValue = WrapWithEncryption(value);
                        var newAssignment = columnPart + encryptedValue;

                        sql = sql.Replace(match.Value, newAssignment);
                    }
                }
            }

            return sql;
        }

        private List<string> SplitValues(string valuesString)
        {
            var values = new List<string>();
            var current = "";
            var inQuotes = false;
            var quoteChar = '\0';
            var parenLevel = 0;

            for (int i = 0; i < valuesString.Length; i++)
            {
                var c = valuesString[i];

                if (!inQuotes && (c == '\'' || c == '"'))
                {
                    inQuotes = true;
                    quoteChar = c;
                    current += c;
                }
                else if (inQuotes && c == quoteChar)
                {
                    inQuotes = false;
                    current += c;
                }
                else if (!inQuotes && c == '(')
                {
                    parenLevel++;
                    current += c;
                }
                else if (!inQuotes && c == ')')
                {
                    parenLevel--;
                    current += c;
                }
                else if (!inQuotes && c == ',' && parenLevel == 0)
                {
                    values.Add(current.Trim());
                    current = "";
                }
                else
                {
                    current += c;
                }
            }

            if (!string.IsNullOrEmpty(current))
            {
                values.Add(current.Trim());
            }

            return values;
        }
    }
}
