#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Runs the InnoBook data encryption migration script

.DESCRIPTION
    This script helps migrate existing unencrypted data to encrypted format.
    It provides options for dry-run, verification, and forced execution.

.PARAMETER DryRun
    Run in dry-run mode to preview what would be migrated without making changes

.PARAMETER Verify
    Verify existing migration by testing decryption

.PARAMETER Force
    Skip confirmation prompts

.PARAMETER Environment
    Specify the environment (Development, Production, Test)

.EXAMPLE
    .\run-encryption-migration.ps1 -DryRun
    Preview what would be migrated

.EXAMPLE
    .\run-encryption-migration.ps1 -Force
    Run the migration without confirmation

.EXAMPLE
    .\run-encryption-migration.ps1 -Verify
    Verify existing migration
#>

param(
    [switch]$DryRun,
    [switch]$Verify,
    [switch]$Force,
    [string]$Environment = "Development"
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Colors for output
$Red = "`e[31m"
$Green = "`e[32m"
$Yellow = "`e[33m"
$Blue = "`e[34m"
$Magenta = "`e[35m"
$Cyan = "`e[36m"
$Reset = "`e[0m"

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = $Reset
    )
    Write-Host "$Color$Message$Reset"
}

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-ColorOutput "=" * 60 $Cyan
    Write-ColorOutput "  $Title" $Cyan
    Write-ColorOutput "=" * 60 $Cyan
    Write-Host ""
}

function Write-Step {
    param([string]$Message)
    Write-ColorOutput "🔄 $Message" $Blue
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" $Green
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠️  $Message" $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" $Red
}

# Main script
try {
    Write-Header "InnoBook Data Encryption Migration"

    # Check if we're in the right directory
    if (-not (Test-Path "InnoBook.sln")) {
        Write-Error "Please run this script from the InnoBook project root directory"
        exit 1
    }

    # Set environment
    $env:ASPNETCORE_ENVIRONMENT = $Environment
    Write-ColorOutput "Environment: $Environment" $Magenta

    # Build the project first
    Write-Step "Building the project..."
    dotnet build --configuration Release --verbosity minimal
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Build failed"
        exit 1
    }
    Write-Success "Build completed"

    # Prepare arguments
    $args = @()
    if ($DryRun) {
        $args += "--dry-run"
        Write-ColorOutput "Mode: DRY RUN (no changes will be made)" $Yellow
    }
    if ($Verify) {
        $args += "--verify"
        Write-ColorOutput "Mode: VERIFY (testing existing encryption)" $Yellow
    }
    if ($Force) {
        $args += "--force"
        Write-ColorOutput "Mode: FORCE (skipping confirmations)" $Yellow
    }

    # Create a temporary program file to run the migration
    $tempProgram = @"
using InnoBook.Scripts;

namespace InnoBook.TempMigration
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            await MigrationRunner.Main(args);
        }
    }
}
"@

    $tempFile = "TempMigrationProgram.cs"
    $tempProgram | Out-File -FilePath $tempFile -Encoding UTF8

    try {
        # Run the migration
        Write-Step "Starting migration..."
        
        # Use dotnet run with the temporary program
        $processArgs = @("run", "--project", ".", "--", $args)
        $processArgs = $processArgs | Where-Object { $_ -ne "" }
        
        # Set environment variables
        $env:DOTNET_ENVIRONMENT = $Environment
        
        # Run the migration
        & dotnet @processArgs
        
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Migration failed with exit code $LASTEXITCODE"
            exit 1
        }

        Write-Success "Migration completed successfully!"
        
        if (-not $DryRun -and -not $Verify) {
            Write-Host ""
            Write-ColorOutput "📋 Next Steps:" $Cyan
            Write-Host "1. Verify the migration worked correctly"
            Write-Host "2. Test your application with encrypted data"
            Write-Host "3. Monitor application logs for any issues"
            Write-Host "4. Consider running: .\run-encryption-migration.ps1 -Verify"
        }
    }
    finally {
        # Clean up temporary file
        if (Test-Path $tempFile) {
            Remove-Item $tempFile -Force
        }
    }
}
catch {
    Write-Error "An error occurred: $($_.Exception.Message)"
    Write-Host $_.ScriptStackTrace
    exit 1
}

Write-Host ""
Write-ColorOutput "Migration script completed." $Green
