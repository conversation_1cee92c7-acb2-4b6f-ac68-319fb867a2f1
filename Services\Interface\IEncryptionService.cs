using InnoBook.Entities;

namespace InnoBook.Services.Interface
{
    public interface IEncryptionService
    {
        /// <summary>
        /// Encrypts a string value using the configured encryption key
        /// </summary>
        /// <param name="plainText">The text to encrypt</param>
        /// <returns>Base64 encoded encrypted data</returns>
        Task<string?> EncryptAsync(string? plainText);

        /// <summary>
        /// Decrypts a Base64 encoded encrypted string
        /// </summary>
        /// <param name="encryptedText">The Base64 encoded encrypted text</param>
        /// <returns>Decrypted plain text</returns>
        Task<string?> DecryptAsync(string? encryptedText);

        /// <summary>
        /// Encrypts sensitive user fields
        /// </summary>
        /// <param name="user">User entity to encrypt</param>
        /// <returns>User with encrypted fields</returns>
        Task<User> EncryptUserAsync(User user);

        /// <summary>
        /// Decrypts sensitive user fields
        /// </summary>
        /// <param name="user">User entity to decrypt</param>
        /// <returns>User with decrypted fields</returns>
        Task<User> DecryptUserAsync(User user);

        /// <summary>
        /// Encrypts sensitive client fields
        /// </summary>
        /// <param name="client">Client entity to encrypt</param>
        /// <returns>Client with encrypted fields</returns>
        Task<Client> EncryptClientAsync(Client client);

        /// <summary>
        /// Decrypts sensitive client fields
        /// </summary>
        /// <param name="client">Client entity to decrypt</param>
        /// <returns>Client with decrypted fields</returns>
        Task<Client> DecryptClientAsync(Client client);

        /// <summary>
        /// Encrypts sensitive company fields
        /// </summary>
        /// <param name="company">Company entity to encrypt</param>
        /// <returns>Company with encrypted fields</returns>
        Task<Company> EncryptCompanyAsync(Company company);

        /// <summary>
        /// Decrypts sensitive company fields
        /// </summary>
        /// <param name="company">Company entity to decrypt</param>
        /// <returns>Company with decrypted fields</returns>
        Task<Company> DecryptCompanyAsync(Company company);
    }
}
