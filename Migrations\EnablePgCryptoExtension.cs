using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace InnoBook.Migrations
{
    /// <summary>
    /// Migration to enable the pgcrypto extension required for encryption
    /// This migration ensures the pgcrypto extension is available before
    /// any encryption operations are performed.
    /// </summary>
    public partial class EnablePgCryptoExtension : Migration
    {
        /// <summary>
        /// Enable the pgcrypto extension
        /// </summary>
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Enable pgcrypto extension for encryption functions
            migrationBuilder.Sql("CREATE EXTENSION IF NOT EXISTS pgcrypto;");
            
            // Verify the extension is working by testing encryption functions
            migrationBuilder.Sql(@"
                DO $$
                DECLARE
                    test_result TEXT;
                BEGIN
                    -- Test that pgp_sym_encrypt and pgp_sym_decrypt work
                    SELECT pgp_sym_decrypt(pgp_sym_encrypt('test', 'key'), 'key') INTO test_result;
                    
                    IF test_result != 'test' THEN
                        RAISE EXCEPTION 'pgcrypto extension test failed';
                    END IF;
                    
                    RAISE NOTICE 'pgcrypto extension enabled and tested successfully';
                END $$;
            ");
        }

        /// <summary>
        /// Disable the pgcrypto extension (not recommended if encryption is in use)
        /// </summary>
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // WARNING: Only drop the extension if no encrypted data exists
            // This could cause data loss if encryption is already in use
            migrationBuilder.Sql(@"
                DO $$
                BEGIN
                    -- Only drop if no tables are using encryption
                    -- This is a safety check to prevent data loss
                    RAISE WARNING 'Dropping pgcrypto extension - ensure no encrypted data exists!';
                    DROP EXTENSION IF EXISTS pgcrypto;
                EXCEPTION
                    WHEN dependent_objects_still_exist THEN
                        RAISE EXCEPTION 'Cannot drop pgcrypto extension: encrypted data still exists. Manual intervention required.';
                END $$;
            ");
        }
    }
}
